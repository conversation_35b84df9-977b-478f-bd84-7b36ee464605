"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateEngine = void 0;
const mustache_1 = __importDefault(require("mustache"));
const path_1 = __importDefault(require("path"));
/**
 * Template engine for processing template files
 */
class TemplateEngine {
    constructor(logger, fileUtils) {
        this.logger = logger;
        this.fileUtils = fileUtils;
    }
    /**
     * Process a template file with the given context
     */
    async processTemplate(templatePath, context) {
        try {
            const templateContent = await this.fileUtils.readFile(templatePath);
            return mustache_1.default.render(templateContent, context);
        }
        catch (error) {
            throw new Error(`Failed to process template ${templatePath}: ${error}`);
        }
    }
    /**
     * Process multiple template files
     */
    async processTemplates(templates, context, outputDir) {
        this.logger.startSpinner('Processing templates...');
        for (const template of templates) {
            try {
                const sourcePath = this.getTemplatePath(template.source);
                const destinationPath = path_1.default.join(outputDir, template.destination);
                if (template.isTemplate) {
                    // Process as Mustache template
                    const processedContent = await this.processTemplate(sourcePath, context);
                    await this.fileUtils.writeFile(destinationPath, processedContent);
                }
                else {
                    // Copy file as-is
                    await this.fileUtils.copyFile(sourcePath, destinationPath);
                }
                this.logger.debug(`Processed: ${template.source} -> ${template.destination}`);
            }
            catch (error) {
                this.logger.failSpinner(`Failed to process template: ${template.source}`);
                throw error;
            }
        }
        this.logger.succeedSpinner('Templates processed successfully');
    }
    /**
     * Get the full path to a template file
     */
    getTemplatePath(templateName) {
        return path_1.default.join(__dirname, '../../templates', templateName);
    }
    /**
     * Create template context with common variables
     */
    createContext(baseContext) {
        const now = new Date();
        return {
            timestamp: now.toISOString(),
            year: now.getFullYear(),
            project: {
                name: '',
                description: '',
                version: '1.0.0',
                license: 'MIT',
                ...baseContext.project,
            },
            ...baseContext,
        };
    }
    /**
     * Validate template context
     */
    validateContext(context) {
        if (!context.project?.name) {
            throw new Error('Project name is required in template context');
        }
        // Add more validation as needed
    }
    /**
     * Get available templates
     */
    async getAvailableTemplates() {
        const templatesDir = path_1.default.join(__dirname, '../../templates');
        try {
            const exists = await this.fileUtils.fileExists(templatesDir);
            if (!exists) {
                return [];
            }
            // This would be implemented to scan template directories
            // For now, return hardcoded list
            return ['express-api', 'react-frontend', 'docker-compose'];
        }
        catch (error) {
            this.logger.debug(`Error getting available templates: ${error}`);
            return [];
        }
    }
}
exports.TemplateEngine = TemplateEngine;
//# sourceMappingURL=template-engine.js.map