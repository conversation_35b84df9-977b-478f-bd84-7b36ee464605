import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
import { TemplateEngine } from './template-engine.js';
import { ServiceConfig, ProjectConfig, GeneratorOptions } from '../types/index.js';
/**
 * Generator for backend microservices
 */
export declare class ServiceGenerator {
    private logger;
    private fileUtils;
    private templateEngine;
    constructor(logger: Logger, fileUtils: FileUtils, templateEngine: TemplateEngine);
    /**
     * Generate a backend service
     */
    generateService(servicePath: string, serviceConfig: ServiceConfig, projectConfig: ProjectConfig, options: GeneratorOptions): Promise<void>;
    /**
     * Get service template based on configuration
     */
    private getServiceTemplate;
    /**
     * Add database support to template
     */
    private addDatabaseSupport;
    /**
     * Add authentication support to template
     */
    private addAuthenticationSupport;
    /**
     * Add Swagger documentation support
     */
    private addSwaggerSupport;
    /**
     * Create service context for templates
     */
    private createServiceContext;
    /**
     * Generate service files from templates
     */
    private generateServiceFiles;
    /**
     * Generate package.json for the service
     */
    private generatePackageJson;
    /**
     * Generate Dockerfile for the service
     */
    private generateDockerfile;
    /**
     * Generate environment files
     */
    private generateEnvironmentFiles;
    /**
     * Create dependency object with latest versions
     */
    private createDependencyObject;
}
//# sourceMappingURL=service-generator.d.ts.map