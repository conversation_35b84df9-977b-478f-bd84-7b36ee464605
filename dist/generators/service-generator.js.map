{"version": 3, "file": "service-generator.js", "sourceRoot": "", "sources": ["../../src/generators/service-generator.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AAYxB;;GAEG;AACH,MAAa,gBAAgB;IAK3B,YAAY,MAAc,EAAE,SAAoB,EAAE,cAA8B;QAC9E,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,WAAmB,EACnB,aAA4B,EAC5B,aAA4B,EAC5B,OAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,aAAa,CAAC,IAAI,aAAa,CAAC,CAAC;QAExE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAExE,yBAAyB;YACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzE,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE7F,sBAAsB;YACtB,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAEnE,6BAA6B;YAC7B,MAAM,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAEzE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,aAAa,CAAC,IAAI,yBAAyB,CAAC,CAAC;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,8BAA8B,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;YAC5E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,aAA4B;QACrD,MAAM,YAAY,GAAoB;YACpC,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE,wBAAwB;YACrC,KAAK,EAAE;gBACL,EAAE,MAAM,EAAE,sBAAsB,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE;gBACjF,EAAE,MAAM,EAAE,oBAAoB,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC7E,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,qBAAqB,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC/F,EAAE,MAAM,EAAE,8BAA8B,EAAE,WAAW,EAAE,sBAAsB,EAAE,UAAU,EAAE,IAAI,EAAE;gBACjG,EAAE,MAAM,EAAE,iCAAiC,EAAE,WAAW,EAAE,yBAAyB,EAAE,UAAU,EAAE,IAAI,EAAE;gBACvG,EAAE,MAAM,EAAE,iCAAiC,EAAE,WAAW,EAAE,yBAAyB,EAAE,UAAU,EAAE,IAAI,EAAE;gBACvG,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,qBAAqB,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC/F,EAAE,MAAM,EAAE,4BAA4B,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC7F,EAAE,MAAM,EAAE,uBAAuB,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,IAAI,EAAE;gBACnF,EAAE,MAAM,EAAE,oBAAoB,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE;aAC/E;YACD,YAAY,EAAE;gBACZ,SAAS;gBACT,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,aAAa;gBACb,QAAQ;aACT;YACD,eAAe,EAAE;gBACf,aAAa;gBACb,gBAAgB;gBAChB,aAAa;gBACb,eAAe;gBACf,YAAY;gBACZ,SAAS;gBACT,SAAS;gBACT,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,WAAW;gBACX,kBAAkB;aACnB;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,oBAAoB;gBAC7B,KAAK,EAAE,sBAAsB;gBAC7B,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,MAAM;gBACd,YAAY,EAAE,cAAc;gBAC5B,MAAM,EAAE,oBAAoB;gBAC5B,UAAU,EAAE,0BAA0B;aACvC;SACF,CAAC;QAEF,+CAA+C;QAC/C,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAChE,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC;QAED,6BAA6B;QAC7B,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;YACjC,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;QAED,4BAA4B;QAC5B,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAyB,EAAE,QAAgB;QACpE,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,+BAA+B,EAAE,WAAW,EAAE,uBAAuB,EAAE,UAAU,EAAE,IAAI,EAAE,EACnG,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,qBAAqB,EAAE,UAAU,EAAE,IAAI,EAAE,CAChG,CAAC;QAEF,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,YAAY;gBACf,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACzC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,OAAO;gBACV,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,SAAS;gBACZ,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACvC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACjD,MAAM;YACR,KAAK,QAAQ;gBACX,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC9C,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,QAAyB;QACxD,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,gCAAgC,EAAE,WAAW,EAAE,wBAAwB,EAAE,UAAU,EAAE,IAAI,EAAE,EACrG,EAAE,MAAM,EAAE,4BAA4B,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE,CAC9F,CAAC;QAEF,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACvD,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAyB;QACjD,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,qBAAqB,EAAE,UAAU,EAAE,IAAI,EAAE,CAChG,CAAC;QAEF,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;QAClE,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,2BAA2B,EAAE,sBAAsB,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,aAA4B,EAAE,aAA4B;QACrF,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;YACvC,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,aAAa;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAChC,WAAmB,EACnB,QAAyB,EACzB,OAAwB,EACxB,OAAyB;QAEzB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,QAAQ,CAAC,KAAK,CAAC,MAAM,oBAAoB,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,WAAmB,EACnB,aAA4B,EAC5B,aAA4B,EAC5B,QAAyB,EACzB,OAAyB;QAEzB,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,GAAG,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,EAAE;YACnD,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,OAAO;YACzC,WAAW,EAAE,GAAG,aAAa,CAAC,IAAI,gBAAgB,aAAa,CAAC,IAAI,EAAE;YACtE,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,YAAY,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,YAAY,CAAC;YAChE,eAAe,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,eAAe,CAAC;YACtE,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,EAAE;YAClC,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,KAAK;YACvC,OAAO,EAAE;gBACP,IAAI,EAAE,UAAU;aACjB;SACF,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAC5B,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EACtC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CACrC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,WAAmB,EACnB,aAA4B,EAC5B,OAAyB;QAEzB,MAAM,iBAAiB,GAAG;;;;;;;;;;SAUrB,aAAa,CAAC,IAAI;;;;;CAK1B,CAAC;QAEE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CACpC,WAAmB,EACnB,aAA4B,EAC5B,OAAyB;QAEzB,MAAM,UAAU,GAAG;OAChB,aAAa,CAAC,IAAI;EACvB,aAAa,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,gCAAgC,aAAa,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;EAChG,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,EAAE;CACjE,CAAC;QAEE,MAAM,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAE1D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC;YAC3E,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,YAAsB;QACnD,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACtC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;YACpB,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;IACnC,CAAC;CACF;AAhSD,4CAgSC"}