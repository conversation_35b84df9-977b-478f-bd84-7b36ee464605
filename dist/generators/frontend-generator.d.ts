import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
import { TemplateEngine } from './template-engine.js';
import { FrontendConfig, ProjectConfig, GeneratorOptions } from '../types/index.js';
/**
 * Generator for frontend applications
 */
export declare class FrontendGenerator {
    private logger;
    private fileUtils;
    private templateEngine;
    constructor(logger: Logger, fileUtils: FileUtils, templateEngine: TemplateEngine);
    /**
     * Generate a frontend application
     */
    generateFrontend(frontendPath: string, frontendConfig: FrontendConfig, projectConfig: ProjectConfig, options: GeneratorOptions): Promise<void>;
    /**
     * Get frontend template based on configuration
     */
    private getFrontendTemplate;
    /**
     * Get Next.js template based on configuration
     */
    private getNextJSTemplate;
    /**
     * Add routing support to template
     */
    private addRoutingSupport;
    /**
     * Add styling framework support
     */
    private addStylingSupport;
    /**
     * Add state management support
     */
    private addStateManagement;
    /**
     * Create frontend context for templates
     */
    private createFrontendContext;
    /**
     * Generate frontend files from templates
     */
    private generateFrontendFiles;
    /**
     * Generate package.json for the frontend
     */
    private generatePackageJson;
    /**
     * Generate configuration files
     */
    private generateConfigFiles;
    /**
     * Generate Dockerfile for the frontend
     */
    private generateDockerfile;
    /**
     * Add Next.js styling framework support
     */
    private addNextJSStylingSupport;
    /**
     * Add Next.js state management support
     */
    private addNextJSStateManagement;
    /**
     * Create dependency object with latest versions
     */
    private createDependencyObject;
}
//# sourceMappingURL=frontend-generator.d.ts.map