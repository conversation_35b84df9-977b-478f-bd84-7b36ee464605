import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
import { ProjectConfig, ServiceConfig, FrontendConfig, GeneratorOptions } from '../types/index.js';
/**
 * Main project generator that orchestrates the creation of microservice projects
 */
export declare class ProjectGenerator {
    private logger;
    private fileUtils;
    private templateEngine;
    private serviceGenerator;
    private frontendGenerator;
    constructor(logger: Logger, fileUtils: FileUtils);
    /**
     * Generate a complete microservice project
     */
    generateProject(projectConfig: ProjectConfig, services: ServiceConfig[], frontend: FrontendConfig | undefined, options: GeneratorOptions): Promise<void>;
    /**
     * Validate project setup before generation
     */
    private validateProjectSetup;
    /**
     * Create basic project structure
     */
    private createProjectStructure;
    /**
     * Generate all services
     */
    private generateServices;
    /**
     * Generate frontend application
     */
    private generateFrontend;
    /**
     * Generate Docker and orchestration files
     */
    private generateOrchestration;
    /**
     * Generate root project files
     */
    private generateRootFiles;
    /**
     * Confirm overwrite of existing directory
     */
    private confirmOverwrite;
    /**
     * Generate docker-compose.yml content
     */
    private generateDockerCompose;
    /**
     * Generate README.md content
     */
    private generateReadme;
    /**
     * Generate root package.json content
     */
    private generateRootPackageJson;
}
//# sourceMappingURL=project-generator.d.ts.map