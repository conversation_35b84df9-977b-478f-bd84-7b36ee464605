"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FrontendGenerator = void 0;
const path_1 = __importDefault(require("path"));
/**
 * Generator for frontend applications
 */
class FrontendGenerator {
    constructor(logger, fileUtils, templateEngine) {
        this.logger = logger;
        this.fileUtils = fileUtils;
        this.templateEngine = templateEngine;
    }
    /**
     * Generate a frontend application
     */
    async generateFrontend(frontendPath, frontendConfig, projectConfig, options) {
        this.logger.startSpinner(`Generating ${frontendConfig.name} frontend...`);
        try {
            const template = this.getFrontendTemplate(frontendConfig);
            const context = this.createFrontendContext(frontendConfig, projectConfig);
            // Generate frontend files
            await this.generateFrontendFiles(frontendPath, template, context, options);
            // Generate package.json
            await this.generatePackageJson(frontendPath, frontendConfig, projectConfig, template, options);
            // Generate configuration files
            await this.generateConfigFiles(frontendPath, frontendConfig, options);
            // Generate Dockerfile
            await this.generateDockerfile(frontendPath, frontendConfig, options);
            this.logger.succeedSpinner(`Frontend ${frontendConfig.name} generated successfully`);
        }
        catch (error) {
            this.logger.failSpinner(`Failed to generate frontend ${frontendConfig.name}`);
            throw error;
        }
    }
    /**
     * Get frontend template based on configuration
     */
    getFrontendTemplate(frontendConfig) {
        const baseTemplate = {
            name: 'react-vite',
            description: 'React application with Vite',
            files: [
                { source: 'frontend/index.html', destination: 'index.html', isTemplate: true },
                { source: 'frontend/src/main.tsx', destination: 'src/main.tsx', isTemplate: true },
                { source: 'frontend/src/App.tsx', destination: 'src/App.tsx', isTemplate: true },
                { source: 'frontend/src/App.css', destination: 'src/App.css', isTemplate: false },
                { source: 'frontend/src/index.css', destination: 'src/index.css', isTemplate: false },
                { source: 'frontend/src/components/Layout.tsx', destination: 'src/components/Layout.tsx', isTemplate: true },
                { source: 'frontend/src/pages/Home.tsx', destination: 'src/pages/Home.tsx', isTemplate: true },
                { source: 'frontend/src/services/api.ts', destination: 'src/services/api.ts', isTemplate: true },
                { source: 'frontend/src/types/index.ts', destination: 'src/types/index.ts', isTemplate: true },
                { source: 'frontend/public/vite.svg', destination: 'public/vite.svg', isTemplate: false },
                { source: 'frontend/.gitignore', destination: '.gitignore', isTemplate: false },
            ],
            dependencies: [
                'react',
                'react-dom',
                'axios',
            ],
            devDependencies: [
                '@types/react',
                '@types/react-dom',
                '@vitejs/plugin-react',
                'typescript',
                'vite',
                'eslint',
                '@typescript-eslint/eslint-plugin',
                '@typescript-eslint/parser',
                'eslint-plugin-react-hooks',
                'eslint-plugin-react-refresh',
            ],
            scripts: {
                'dev': 'vite',
                'build': 'tsc && vite build',
                'lint': 'eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0',
                'preview': 'vite preview',
                'test': 'vitest',
            },
        };
        // Add routing support
        if (frontendConfig.routing) {
            this.addRoutingSupport(baseTemplate, frontendConfig.framework);
        }
        // Add styling framework
        if (frontendConfig.styling !== 'none') {
            this.addStylingSupport(baseTemplate, frontendConfig.styling);
        }
        // Add state management
        if (frontendConfig.stateManagement && frontendConfig.stateManagement !== 'none') {
            this.addStateManagement(baseTemplate, frontendConfig.stateManagement);
        }
        return baseTemplate;
    }
    /**
     * Add routing support to template
     */
    addRoutingSupport(template, framework) {
        if (framework === 'react') {
            template.files.push({ source: 'frontend/src/router/index.tsx', destination: 'src/router/index.tsx', isTemplate: true }, { source: 'frontend/src/pages/About.tsx', destination: 'src/pages/About.tsx', isTemplate: true });
            template.dependencies.push('react-router-dom');
            template.devDependencies.push('@types/react-router-dom');
        }
    }
    /**
     * Add styling framework support
     */
    addStylingSupport(template, styling) {
        switch (styling) {
            case 'tailwind':
                template.files.push({ source: 'frontend/tailwind.config.js', destination: 'tailwind.config.js', isTemplate: true }, { source: 'frontend/postcss.config.js', destination: 'postcss.config.js', isTemplate: false });
                template.devDependencies.push('tailwindcss', 'postcss', 'autoprefixer');
                break;
            case 'styled-components':
                template.dependencies.push('styled-components');
                template.devDependencies.push('@types/styled-components');
                break;
            case 'sass':
                template.devDependencies.push('sass');
                break;
        }
    }
    /**
     * Add state management support
     */
    addStateManagement(template, stateManagement) {
        switch (stateManagement) {
            case 'redux':
                template.files.push({ source: 'frontend/src/store/index.ts', destination: 'src/store/index.ts', isTemplate: true }, { source: 'frontend/src/store/slices/appSlice.ts', destination: 'src/store/slices/appSlice.ts', isTemplate: true });
                template.dependencies.push('@reduxjs/toolkit', 'react-redux');
                template.devDependencies.push('@types/react-redux');
                break;
            case 'zustand':
                template.files.push({ source: 'frontend/src/store/useStore.ts', destination: 'src/store/useStore.ts', isTemplate: true });
                template.dependencies.push('zustand');
                break;
            case 'context':
                template.files.push({ source: 'frontend/src/context/AppContext.tsx', destination: 'src/context/AppContext.tsx', isTemplate: true });
                break;
        }
    }
    /**
     * Create frontend context for templates
     */
    createFrontendContext(frontendConfig, projectConfig) {
        return this.templateEngine.createContext({
            project: projectConfig,
            frontend: frontendConfig,
        });
    }
    /**
     * Generate frontend files from templates
     */
    async generateFrontendFiles(frontendPath, template, context, options) {
        if (options.dryRun) {
            this.logger.info(`Would generate ${template.files.length} files for frontend`);
            return;
        }
        await this.templateEngine.processTemplates(template.files, context, frontendPath);
    }
    /**
     * Generate package.json for the frontend
     */
    async generatePackageJson(frontendPath, frontendConfig, projectConfig, template, options) {
        const packageJson = {
            name: `${projectConfig.name}-${frontendConfig.name}`,
            private: true,
            version: projectConfig.version || '1.0.0',
            type: 'module',
            scripts: template.scripts,
            dependencies: this.createDependencyObject(template.dependencies),
            devDependencies: this.createDependencyObject(template.devDependencies),
            author: projectConfig.author || '',
            license: projectConfig.license || 'MIT',
            engines: {
                node: '>=16.0.0',
            },
        };
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(frontendPath, 'package.json'), JSON.stringify(packageJson, null, 2));
        }
    }
    /**
     * Generate configuration files
     */
    async generateConfigFiles(frontendPath, frontendConfig, options) {
        // Generate vite.config.ts
        const viteConfig = `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: '${frontendConfig.apiBaseUrl}',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\\/api/, '')
      }
    }
  }
})
`;
        // Generate tsconfig.json
        const tsConfig = {
            compilerOptions: {
                target: 'ES2020',
                useDefineForClassFields: true,
                lib: ['ES2020', 'DOM', 'DOM.Iterable'],
                module: 'ESNext',
                skipLibCheck: true,
                moduleResolution: 'bundler',
                allowImportingTsExtensions: true,
                resolveJsonModule: true,
                isolatedModules: true,
                noEmit: true,
                jsx: 'react-jsx',
                strict: true,
                noUnusedLocals: true,
                noUnusedParameters: true,
                noFallthroughCasesInSwitch: true,
            },
            include: ['src'],
            references: [{ path: './tsconfig.node.json' }],
        };
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(frontendPath, 'vite.config.ts'), viteConfig);
            await this.fileUtils.writeFile(path_1.default.join(frontendPath, 'tsconfig.json'), JSON.stringify(tsConfig, null, 2));
        }
    }
    /**
     * Generate Dockerfile for the frontend
     */
    async generateDockerfile(frontendPath, frontendConfig, options) {
        const dockerfileContent = `# Build stage
FROM node:18-alpine as build

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
`;
        const nginxConfig = `events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /api {
            proxy_pass ${frontendConfig.apiBaseUrl};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
`;
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(frontendPath, 'Dockerfile'), dockerfileContent);
            await this.fileUtils.writeFile(path_1.default.join(frontendPath, 'nginx.conf'), nginxConfig);
        }
    }
    /**
     * Create dependency object with latest versions
     */
    createDependencyObject(dependencies) {
        return dependencies.reduce((acc, dep) => {
            acc[dep] = 'latest';
            return acc;
        }, {});
    }
}
exports.FrontendGenerator = FrontendGenerator;
//# sourceMappingURL=frontend-generator.js.map