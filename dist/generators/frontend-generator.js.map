{"version": 3, "file": "frontend-generator.js", "sourceRoot": "", "sources": ["../../src/generators/frontend-generator.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AAYxB;;GAEG;AACH,MAAa,iBAAiB;IAK5B,YAAY,MAAc,EAAE,SAAoB,EAAE,cAA8B;QAC9E,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,YAAoB,EACpB,cAA8B,EAC9B,aAA4B,EAC5B,OAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,cAAc,CAAC,IAAI,cAAc,CAAC,CAAC;QAE1E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YAE1E,0BAA0B;YAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAE3E,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE/F,+BAA+B;YAC/B,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAEtE,sBAAsB;YACtB,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAErE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,cAAc,CAAC,IAAI,yBAAyB,CAAC,CAAC;QACvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,+BAA+B,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,cAA8B;QACxD,MAAM,YAAY,GAAoB;YACpC,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,6BAA6B;YAC1C,KAAK,EAAE;gBACL,EAAE,MAAM,EAAE,qBAAqB,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC9E,EAAE,MAAM,EAAE,uBAAuB,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE;gBAClF,EAAE,MAAM,EAAE,sBAAsB,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE;gBAChF,EAAE,MAAM,EAAE,sBAAsB,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,KAAK,EAAE;gBACjF,EAAE,MAAM,EAAE,wBAAwB,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,KAAK,EAAE;gBACrF,EAAE,MAAM,EAAE,oCAAoC,EAAE,WAAW,EAAE,2BAA2B,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC5G,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC9F,EAAE,MAAM,EAAE,8BAA8B,EAAE,WAAW,EAAE,qBAAqB,EAAE,UAAU,EAAE,IAAI,EAAE;gBAChG,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE;gBAC9F,EAAE,MAAM,EAAE,0BAA0B,EAAE,WAAW,EAAE,iBAAiB,EAAE,UAAU,EAAE,KAAK,EAAE;gBACzF,EAAE,MAAM,EAAE,qBAAqB,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE;aAChF;YACD,YAAY,EAAE;gBACZ,OAAO;gBACP,WAAW;gBACX,OAAO;aACR;YACD,eAAe,EAAE;gBACf,cAAc;gBACd,kBAAkB;gBAClB,sBAAsB;gBACtB,YAAY;gBACZ,MAAM;gBACN,QAAQ;gBACR,kCAAkC;gBAClC,2BAA2B;gBAC3B,2BAA2B;gBAC3B,6BAA6B;aAC9B;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,MAAM;gBACb,OAAO,EAAE,mBAAmB;gBAC5B,MAAM,EAAE,2EAA2E;gBACnF,SAAS,EAAE,cAAc;gBACzB,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC;QAEF,sBAAsB;QACtB,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;QAED,wBAAwB;QACxB,IAAI,cAAc,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YACtC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;QAED,uBAAuB;QACvB,IAAI,cAAc,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe,KAAK,MAAM,EAAE,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,eAAe,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAyB,EAAE,SAAiB;QACpE,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,+BAA+B,EAAE,WAAW,EAAE,sBAAsB,EAAE,UAAU,EAAE,IAAI,EAAE,EAClG,EAAE,MAAM,EAAE,8BAA8B,EAAE,WAAW,EAAE,qBAAqB,EAAE,UAAU,EAAE,IAAI,EAAE,CACjG,CAAC;YACF,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC/C,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAyB,EAAE,OAAe;QAClE,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,UAAU;gBACb,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE,EAC9F,EAAE,MAAM,EAAE,4BAA4B,EAAE,WAAW,EAAE,mBAAmB,EAAE,UAAU,EAAE,KAAK,EAAE,CAC9F,CAAC;gBACF,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;gBACxE,MAAM;YACR,KAAK,mBAAmB;gBACtB,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAChD,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,MAAM;gBACT,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtC,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAyB,EAAE,eAAuB;QAC3E,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,OAAO;gBACV,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE,EAC9F,EAAE,MAAM,EAAE,uCAAuC,EAAE,WAAW,EAAE,8BAA8B,EAAE,UAAU,EAAE,IAAI,EAAE,CACnH,CAAC;gBACF,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;gBAC9D,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACpD,MAAM;YACR,KAAK,SAAS;gBACZ,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,gCAAgC,EAAE,WAAW,EAAE,uBAAuB,EAAE,UAAU,EAAE,IAAI,EAAE,CACrG,CAAC;gBACF,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,SAAS;gBACZ,QAAQ,CAAC,KAAK,CAAC,IAAI,CACjB,EAAE,MAAM,EAAE,qCAAqC,EAAE,WAAW,EAAE,4BAA4B,EAAE,UAAU,EAAE,IAAI,EAAE,CAC/G,CAAC;gBACF,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,cAA8B,EAAE,aAA4B;QACxF,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;YACvC,OAAO,EAAE,aAAa;YACtB,QAAQ,EAAE,cAAc;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,YAAoB,EACpB,QAAyB,EACzB,OAAwB,EACxB,OAAyB;QAEzB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,QAAQ,CAAC,KAAK,CAAC,MAAM,qBAAqB,CAAC,CAAC;YAC/E,OAAO;QACT,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,YAAoB,EACpB,cAA8B,EAC9B,aAA4B,EAC5B,QAAyB,EACzB,OAAyB;QAEzB,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,GAAG,aAAa,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE;YACpD,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,OAAO;YACzC,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,YAAY,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,YAAY,CAAC;YAChE,eAAe,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,eAAe,CAAC;YACtE,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,EAAE;YAClC,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,KAAK;YACvC,OAAO,EAAE;gBACP,IAAI,EAAE,UAAU;aACjB;SACF,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAC5B,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,EACvC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CACrC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,YAAoB,EACpB,cAA8B,EAC9B,OAAyB;QAEzB,0BAA0B;QAC1B,MAAM,UAAU,GAAG;;;;;;;;;mBASJ,cAAc,CAAC,UAAU;;;;;;;CAO3C,CAAC;QAEE,yBAAyB;QACzB,MAAM,QAAQ,GAAG;YACf,eAAe,EAAE;gBACf,MAAM,EAAE,QAAQ;gBAChB,uBAAuB,EAAE,IAAI;gBAC7B,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,cAAc,CAAC;gBACtC,MAAM,EAAE,QAAQ;gBAChB,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,SAAS;gBAC3B,0BAA0B,EAAE,IAAI;gBAChC,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,IAAI;gBACrB,MAAM,EAAE,IAAI;gBACZ,GAAG,EAAE,WAAW;gBAChB,MAAM,EAAE,IAAI;gBACZ,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,0BAA0B,EAAE,IAAI;aACjC;YACD,OAAO,EAAE,CAAC,KAAK,CAAC;YAChB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC;SAC/C,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,EAAE,UAAU,CAAC,CAAC;YACtF,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAC5B,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,EACxC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAClC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,YAAoB,EACpB,cAA8B,EAC9B,OAAyB;QAEzB,MAAM,iBAAiB,GAAG;;;;;;;;;;;;;;;;;;;;CAoB7B,CAAC;QAEE,MAAM,WAAW,GAAG;;;;;;;;;;;;;;;;;;;yBAmBC,cAAc,CAAC,UAAU;;;;;;CAMjD,CAAC;QAEE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE,iBAAiB,CAAC,CAAC;YACzF,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,YAAsB;QACnD,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACtC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;YACpB,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;IACnC,CAAC;CACF;AA3WD,8CA2WC"}