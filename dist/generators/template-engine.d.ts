import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
import { TemplateContext, TemplateFile } from '../types/index.js';
/**
 * Template engine for processing template files
 */
export declare class TemplateEngine {
    private logger;
    private fileUtils;
    constructor(logger: Logger, fileUtils: FileUtils);
    /**
     * Process a template file with the given context
     */
    processTemplate(templatePath: string, context: TemplateContext): Promise<string>;
    /**
     * Process multiple template files
     */
    processTemplates(templates: TemplateFile[], context: TemplateContext, outputDir: string): Promise<void>;
    /**
     * Get the full path to a template file
     */
    private getTemplatePath;
    /**
     * Create template context with common variables
     */
    createContext(baseContext: Partial<TemplateContext>): TemplateContext;
    /**
     * Validate template context
     */
    validateContext(context: TemplateContext): void;
    /**
     * Get available templates
     */
    getAvailableTemplates(): Promise<string[]>;
}
//# sourceMappingURL=template-engine.d.ts.map