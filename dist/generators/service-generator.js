"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceGenerator = void 0;
const path_1 = __importDefault(require("path"));
/**
 * Generator for backend microservices
 */
class ServiceGenerator {
    constructor(logger, fileUtils, templateEngine) {
        this.logger = logger;
        this.fileUtils = fileUtils;
        this.templateEngine = templateEngine;
    }
    /**
     * Generate a backend service
     */
    async generateService(servicePath, serviceConfig, projectConfig, options) {
        this.logger.startSpinner(`Generating ${serviceConfig.name} service...`);
        try {
            const template = this.getServiceTemplate(serviceConfig);
            const context = this.createServiceContext(serviceConfig, projectConfig);
            // Generate service files
            await this.generateServiceFiles(servicePath, template, context, options);
            // Generate package.json
            await this.generatePackageJson(servicePath, serviceConfig, projectConfig, template, options);
            // Generate Dockerfile
            await this.generateDockerfile(servicePath, serviceConfig, options);
            // Generate environment files
            await this.generateEnvironmentFiles(servicePath, serviceConfig, options);
            this.logger.succeedSpinner(`Service ${serviceConfig.name} generated successfully`);
        }
        catch (error) {
            this.logger.failSpinner(`Failed to generate service ${serviceConfig.name}`);
            throw error;
        }
    }
    /**
     * Get service template based on configuration
     */
    getServiceTemplate(serviceConfig) {
        if (serviceConfig.framework === 'nestjs') {
            return this.getNestJSTemplate(serviceConfig);
        }
        const baseTemplate = {
            name: 'express-api',
            description: 'Express.js API service',
            files: [
                { source: 'service/src/index.ts', destination: 'src/index.ts', isTemplate: true },
                { source: 'service/src/app.ts', destination: 'src/app.ts', isTemplate: true },
                { source: 'service/src/routes/index.ts', destination: 'src/routes/index.ts', isTemplate: true },
                { source: 'service/src/routes/health.ts', destination: 'src/routes/health.ts', isTemplate: true },
                { source: 'service/src/middleware/index.ts', destination: 'src/middleware/index.ts', isTemplate: true },
                { source: 'service/src/middleware/error.ts', destination: 'src/middleware/error.ts', isTemplate: true },
                { source: 'service/src/config/index.ts', destination: 'src/config/index.ts', isTemplate: true },
                { source: 'service/src/types/index.ts', destination: 'src/types/index.ts', isTemplate: true },
                { source: 'service/tsconfig.json', destination: 'tsconfig.json', isTemplate: true },
                { source: 'service/.gitignore', destination: '.gitignore', isTemplate: false },
            ],
            dependencies: [
                'express',
                'cors',
                'helmet',
                'morgan',
                'compression',
                'dotenv',
            ],
            devDependencies: [
                '@types/node',
                '@types/express',
                '@types/cors',
                '@types/morgan',
                'typescript',
                'ts-node',
                'nodemon',
                'jest',
                '@types/jest',
                'ts-jest',
                'supertest',
                '@types/supertest',
            ],
            scripts: {
                'start': 'node dist/index.js',
                'dev': 'nodemon src/index.ts',
                'build': 'tsc',
                'test': 'jest',
                'test:watch': 'jest --watch',
                'lint': 'eslint src/**/*.ts',
                'lint:fix': 'eslint src/**/*.ts --fix',
            },
        };
        // Add database-specific dependencies and files
        if (serviceConfig.database && serviceConfig.database !== 'none') {
            this.addDatabaseSupport(baseTemplate, serviceConfig.database);
        }
        // Add authentication support
        if (serviceConfig.authentication) {
            this.addAuthenticationSupport(baseTemplate);
        }
        // Add Swagger documentation
        if (serviceConfig.swagger) {
            this.addSwaggerSupport(baseTemplate);
        }
        return baseTemplate;
    }
    /**
     * Get NestJS template based on configuration
     */
    getNestJSTemplate(serviceConfig) {
        const baseTemplate = {
            name: 'nestjs-api',
            description: 'NestJS API service with microservice support',
            files: [
                { source: 'nestjs/src/main.ts', destination: 'src/main.ts', isTemplate: true },
                { source: 'nestjs/src/app.module.ts', destination: 'src/app.module.ts', isTemplate: true },
                { source: 'nestjs/src/app.controller.ts', destination: 'src/app.controller.ts', isTemplate: true },
                { source: 'nestjs/src/app.service.ts', destination: 'src/app.service.ts', isTemplate: true },
                { source: 'nestjs/src/health/health.controller.ts', destination: 'src/health/health.controller.ts', isTemplate: true },
                { source: 'nestjs/src/health/health.module.ts', destination: 'src/health/health.module.ts', isTemplate: true },
                { source: 'nestjs/src/common/filters/http-exception.filter.ts', destination: 'src/common/filters/http-exception.filter.ts', isTemplate: true },
                { source: 'nestjs/src/common/interceptors/logging.interceptor.ts', destination: 'src/common/interceptors/logging.interceptor.ts', isTemplate: true },
                { source: 'nestjs/src/config/configuration.ts', destination: 'src/config/configuration.ts', isTemplate: true },
                { source: 'nestjs/tsconfig.json', destination: 'tsconfig.json', isTemplate: true },
                { source: 'nestjs/nest-cli.json', destination: 'nest-cli.json', isTemplate: true },
                { source: 'service/.gitignore', destination: '.gitignore', isTemplate: false },
            ],
            dependencies: [
                '@nestjs/common',
                '@nestjs/core',
                '@nestjs/platform-express',
                '@nestjs/config',
                '@nestjs/terminus',
                'reflect-metadata',
                'rxjs',
            ],
            devDependencies: [
                '@nestjs/cli',
                '@nestjs/schematics',
                '@nestjs/testing',
                '@types/express',
                '@types/jest',
                '@types/node',
                '@types/supertest',
                'jest',
                'source-map-support',
                'supertest',
                'ts-jest',
                'ts-loader',
                'ts-node',
                'tsconfig-paths',
                'typescript',
            ],
            scripts: {
                'build': 'nest build',
                'format': 'prettier --write "src/**/*.ts" "test/**/*.ts"',
                'start': 'nest start',
                'start:dev': 'nest start --watch',
                'start:debug': 'nest start --debug --watch',
                'start:prod': 'node dist/main',
                'lint': 'eslint "{src,apps,libs,test}/**/*.ts" --fix',
                'test': 'jest',
                'test:watch': 'jest --watch',
                'test:cov': 'jest --coverage',
                'test:debug': 'node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand',
                'test:e2e': 'jest --config ./test/jest-e2e.json',
            },
        };
        // Add microservice transport support
        if (serviceConfig.microserviceTransport && serviceConfig.microserviceTransport.length > 0) {
            this.addMicroserviceSupport(baseTemplate, serviceConfig.microserviceTransport);
        }
        // Add database support
        if (serviceConfig.database && serviceConfig.database !== 'none') {
            this.addNestJSDatabaseSupport(baseTemplate, serviceConfig.database);
        }
        // Add authentication support
        if (serviceConfig.authentication) {
            this.addNestJSAuthenticationSupport(baseTemplate);
        }
        // Add Swagger documentation
        if (serviceConfig.swagger) {
            this.addNestJSSwaggerSupport(baseTemplate);
        }
        return baseTemplate;
    }
    /**
     * Add database support to template
     */
    addDatabaseSupport(template, database) {
        template.files.push({ source: 'service/src/database/index.ts', destination: 'src/database/index.ts', isTemplate: true }, { source: 'service/src/models/index.ts', destination: 'src/models/index.ts', isTemplate: true });
        switch (database) {
            case 'postgresql':
                template.dependencies.push('pg', 'knex');
                template.devDependencies.push('@types/pg');
                break;
            case 'mysql':
                template.dependencies.push('mysql2', 'knex');
                break;
            case 'mongodb':
                template.dependencies.push('mongoose');
                template.devDependencies.push('@types/mongoose');
                break;
            case 'sqlite':
                template.dependencies.push('sqlite3', 'knex');
                break;
        }
    }
    /**
     * Add authentication support to template
     */
    addAuthenticationSupport(template) {
        template.files.push({ source: 'service/src/middleware/auth.ts', destination: 'src/middleware/auth.ts', isTemplate: true }, { source: 'service/src/routes/auth.ts', destination: 'src/routes/auth.ts', isTemplate: true });
        template.dependencies.push('jsonwebtoken', 'bcryptjs');
        template.devDependencies.push('@types/jsonwebtoken', '@types/bcryptjs');
    }
    /**
     * Add Swagger documentation support
     */
    addSwaggerSupport(template) {
        template.files.push({ source: 'service/src/docs/swagger.ts', destination: 'src/docs/swagger.ts', isTemplate: true });
        template.dependencies.push('swagger-ui-express', 'swagger-jsdoc');
        template.devDependencies.push('@types/swagger-ui-express', '@types/swagger-jsdoc');
    }
    /**
     * Add microservice transport support to NestJS template
     */
    addMicroserviceSupport(template, transports) {
        template.files.push({ source: 'nestjs/src/microservice/microservice.module.ts', destination: 'src/microservice/microservice.module.ts', isTemplate: true }, { source: 'nestjs/src/microservice/microservice.controller.ts', destination: 'src/microservice/microservice.controller.ts', isTemplate: true }, { source: 'nestjs/src/microservice/microservice.service.ts', destination: 'src/microservice/microservice.service.ts', isTemplate: true });
        transports.forEach(transport => {
            switch (transport) {
                case 'kafka':
                    template.dependencies.push('@nestjs/microservices', 'kafkajs');
                    break;
                case 'grpc':
                    template.dependencies.push('@nestjs/microservices', '@grpc/grpc-js', '@grpc/proto-loader');
                    template.files.push({ source: 'nestjs/src/proto/service.proto', destination: 'src/proto/service.proto', isTemplate: true });
                    break;
                case 'redis':
                    template.dependencies.push('@nestjs/microservices', 'redis');
                    break;
                case 'tcp':
                    template.dependencies.push('@nestjs/microservices');
                    break;
                case 'nats':
                    template.dependencies.push('@nestjs/microservices', 'nats');
                    break;
                case 'rabbitmq':
                    template.dependencies.push('@nestjs/microservices', 'amqplib', 'amqp-connection-manager');
                    template.devDependencies.push('@types/amqplib');
                    break;
            }
        });
    }
    /**
     * Add NestJS database support
     */
    addNestJSDatabaseSupport(template, database) {
        template.files.push({ source: 'nestjs/src/database/database.module.ts', destination: 'src/database/database.module.ts', isTemplate: true });
        switch (database) {
            case 'postgresql':
                template.dependencies.push('@nestjs/typeorm', 'typeorm', 'pg');
                template.devDependencies.push('@types/pg');
                break;
            case 'mysql':
                template.dependencies.push('@nestjs/typeorm', 'typeorm', 'mysql2');
                break;
            case 'mongodb':
                template.dependencies.push('@nestjs/mongoose', 'mongoose');
                template.devDependencies.push('@types/mongoose');
                break;
            case 'sqlite':
                template.dependencies.push('@nestjs/typeorm', 'typeorm', 'sqlite3');
                break;
        }
    }
    /**
     * Add NestJS authentication support
     */
    addNestJSAuthenticationSupport(template) {
        template.files.push({ source: 'nestjs/src/auth/auth.module.ts', destination: 'src/auth/auth.module.ts', isTemplate: true }, { source: 'nestjs/src/auth/auth.controller.ts', destination: 'src/auth/auth.controller.ts', isTemplate: true }, { source: 'nestjs/src/auth/auth.service.ts', destination: 'src/auth/auth.service.ts', isTemplate: true }, { source: 'nestjs/src/auth/guards/jwt-auth.guard.ts', destination: 'src/auth/guards/jwt-auth.guard.ts', isTemplate: true }, { source: 'nestjs/src/auth/strategies/jwt.strategy.ts', destination: 'src/auth/strategies/jwt.strategy.ts', isTemplate: true });
        template.dependencies.push('@nestjs/jwt', '@nestjs/passport', 'passport', 'passport-jwt', 'bcryptjs');
        template.devDependencies.push('@types/passport-jwt', '@types/bcryptjs');
    }
    /**
     * Add NestJS Swagger documentation support
     */
    addNestJSSwaggerSupport(template) {
        template.dependencies.push('@nestjs/swagger');
    }
    /**
     * Create service context for templates
     */
    createServiceContext(serviceConfig, projectConfig) {
        return this.templateEngine.createContext({
            project: projectConfig,
            service: serviceConfig,
        });
    }
    /**
     * Generate service files from templates
     */
    async generateServiceFiles(servicePath, template, context, options) {
        if (options.dryRun) {
            this.logger.info(`Would generate ${template.files.length} files for service`);
            return;
        }
        await this.templateEngine.processTemplates(template.files, context, servicePath);
    }
    /**
     * Generate package.json for the service
     */
    async generatePackageJson(servicePath, serviceConfig, projectConfig, template, options) {
        const packageJson = {
            name: `${projectConfig.name}-${serviceConfig.name}`,
            version: projectConfig.version || '1.0.0',
            description: `${serviceConfig.name} service for ${projectConfig.name}`,
            main: 'dist/index.js',
            scripts: template.scripts,
            dependencies: this.createDependencyObject(template.dependencies),
            devDependencies: this.createDependencyObject(template.devDependencies),
            author: projectConfig.author || '',
            license: projectConfig.license || 'MIT',
            engines: {
                node: '>=16.0.0',
            },
        };
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(servicePath, 'package.json'), JSON.stringify(packageJson, null, 2));
        }
    }
    /**
     * Generate Dockerfile for the service
     */
    async generateDockerfile(servicePath, serviceConfig, options) {
        const dockerfileContent = `FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE ${serviceConfig.port}

USER node

CMD ["npm", "start"]
`;
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(servicePath, 'Dockerfile'), dockerfileContent);
        }
    }
    /**
     * Generate environment files
     */
    async generateEnvironmentFiles(servicePath, serviceConfig, options) {
        const envContent = `NODE_ENV=development
PORT=${serviceConfig.port}
${serviceConfig.database !== 'none' ? `DATABASE_URL=\nDATABASE_NAME=${serviceConfig.name}_db` : ''}
${serviceConfig.authentication ? 'JWT_SECRET=your-secret-key' : ''}
`;
        const envExampleContent = envContent.replace(/=.*/g, '=');
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(servicePath, '.env'), envContent);
            await this.fileUtils.writeFile(path_1.default.join(servicePath, '.env.example'), envExampleContent);
        }
    }
    /**
     * Create dependency object with latest versions
     */
    createDependencyObject(dependencies) {
        return dependencies.reduce((acc, dep) => {
            acc[dep] = 'latest';
            return acc;
        }, {});
    }
}
exports.ServiceGenerator = ServiceGenerator;
//# sourceMappingURL=service-generator.js.map