#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const create_project_js_1 = require("./commands/create-project.js");
const logger_js_1 = require("./utils/logger.js");
const packageJson = require('../package.json');
/**
 * Main CLI application
 */
class MicroserviceCLI {
    constructor() {
        this.program = new commander_1.Command();
        this.logger = new logger_js_1.Logger();
        this.setupProgram();
        this.registerCommands();
    }
    setupProgram() {
        this.program
            .name('microservice-cli')
            .description('A CLI tool for generating microservice projects with frontend and backend')
            .version(packageJson.version)
            .option('-v, --verbose', 'Enable verbose output', false);
    }
    registerCommands() {
        // Register create project command
        const createCommand = new create_project_js_1.CreateProjectCommand();
        createCommand.register(this.program);
        // Add help command
        this.program
            .command('help')
            .description('Display help information')
            .action(() => {
            this.program.help();
        });
    }
    async run() {
        try {
            await this.program.parseAsync(process.argv);
        }
        catch (error) {
            if (error instanceof Error) {
                this.logger.error(error.message);
            }
            else {
                this.logger.error('An unexpected error occurred');
            }
            process.exit(1);
        }
    }
}
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
// Run the CLI
const cli = new MicroserviceCLI();
cli.run().catch((error) => {
    console.error('Failed to run CLI:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map