import { Logger } from './logger.js';
/**
 * File system utilities for the CLI
 */
export declare class FileUtils {
    private logger;
    constructor(logger: Logger);
    /**
     * Check if a directory exists and is not empty
     */
    isDirectoryEmpty(dirPath: string): Promise<boolean>;
    /**
     * Ensure directory exists, create if it doesn't
     */
    ensureDirectory(dirPath: string): Promise<void>;
    /**
     * Copy file with optional template processing
     */
    copyFile(source: string, destination: string): Promise<void>;
    /**
     * Write content to file
     */
    writeFile(filePath: string, content: string): Promise<void>;
    /**
     * Read file content
     */
    readFile(filePath: string): Promise<string>;
    /**
     * Check if file exists
     */
    fileExists(filePath: string): Promise<boolean>;
    /**
     * Get relative path from current working directory
     */
    getRelativePath(filePath: string): string;
    /**
     * Resolve path relative to project root
     */
    resolvePath(...pathSegments: string[]): string;
    /**
     * Get file extension
     */
    getExtension(filePath: string): string;
    /**
     * Get filename without extension
     */
    getBasename(filePath: string): string;
}
//# sourceMappingURL=file-utils.d.ts.map