"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
/**
 * Logger utility for consistent CLI output
 */
class Logger {
    constructor(verbose = false) {
        this.verbose = verbose;
    }
    /**
     * Log an info message
     */
    info(message) {
        console.log(chalk_1.default.blue('ℹ'), message);
    }
    /**
     * Log a success message
     */
    success(message) {
        console.log(chalk_1.default.green('✓'), message);
    }
    /**
     * Log a warning message
     */
    warn(message) {
        console.log(chalk_1.default.yellow('⚠'), message);
    }
    /**
     * Log an error message
     */
    error(message) {
        console.log(chalk_1.default.red('✗'), message);
    }
    /**
     * Log a debug message (only if verbose mode is enabled)
     */
    debug(message) {
        if (this.verbose) {
            console.log(chalk_1.default.gray('🐛'), chalk_1.default.gray(message));
        }
    }
    /**
     * Start a spinner with a message
     */
    startSpinner(message) {
        this.spinner = (0, ora_1.default)(message).start();
    }
    /**
     * Update spinner text
     */
    updateSpinner(message) {
        if (this.spinner) {
            this.spinner.text = message;
        }
    }
    /**
     * Stop spinner with success
     */
    succeedSpinner(message) {
        if (this.spinner) {
            this.spinner.succeed(message);
            this.spinner = undefined;
        }
    }
    /**
     * Stop spinner with failure
     */
    failSpinner(message) {
        if (this.spinner) {
            this.spinner.fail(message);
            this.spinner = undefined;
        }
    }
    /**
     * Stop spinner
     */
    stopSpinner() {
        if (this.spinner) {
            this.spinner.stop();
            this.spinner = undefined;
        }
    }
    /**
     * Log a title/header
     */
    title(message) {
        console.log();
        console.log(chalk_1.default.bold.cyan(message));
        console.log(chalk_1.default.cyan('='.repeat(message.length)));
    }
    /**
     * Log a subtitle
     */
    subtitle(message) {
        console.log();
        console.log(chalk_1.default.bold(message));
    }
}
exports.Logger = Logger;
//# sourceMappingURL=logger.js.map