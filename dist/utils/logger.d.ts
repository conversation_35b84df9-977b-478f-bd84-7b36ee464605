/**
 * Logger utility for consistent CLI output
 */
export declare class Logger {
    private verbose;
    private spinner?;
    constructor(verbose?: boolean);
    /**
     * Log an info message
     */
    info(message: string): void;
    /**
     * Log a success message
     */
    success(message: string): void;
    /**
     * Log a warning message
     */
    warn(message: string): void;
    /**
     * Log an error message
     */
    error(message: string): void;
    /**
     * Log a debug message (only if verbose mode is enabled)
     */
    debug(message: string): void;
    /**
     * Start a spinner with a message
     */
    startSpinner(message: string): void;
    /**
     * Update spinner text
     */
    updateSpinner(message: string): void;
    /**
     * Stop spinner with success
     */
    succeedSpinner(message?: string): void;
    /**
     * Stop spinner with failure
     */
    failSpinner(message?: string): void;
    /**
     * Stop spinner
     */
    stopSpinner(): void;
    /**
     * Log a title/header
     */
    title(message: string): void;
    /**
     * Log a subtitle
     */
    subtitle(message: string): void;
}
//# sourceMappingURL=logger.d.ts.map