"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUtils = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
/**
 * File system utilities for the CLI
 */
class FileUtils {
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Check if a directory exists and is not empty
     */
    async isDirectoryEmpty(dirPath) {
        try {
            const exists = await fs_extra_1.default.pathExists(dirPath);
            if (!exists)
                return true;
            const files = await fs_extra_1.default.readdir(dirPath);
            return files.length === 0;
        }
        catch (error) {
            this.logger.debug(`Error checking directory: ${error}`);
            return true;
        }
    }
    /**
     * Ensure directory exists, create if it doesn't
     */
    async ensureDirectory(dirPath) {
        try {
            await fs_extra_1.default.ensureDir(dirPath);
            this.logger.debug(`Ensured directory exists: ${dirPath}`);
        }
        catch (error) {
            throw new Error(`Failed to create directory ${dirPath}: ${error}`);
        }
    }
    /**
     * Copy file with optional template processing
     */
    async copyFile(source, destination) {
        try {
            await fs_extra_1.default.copy(source, destination);
            this.logger.debug(`Copied file: ${source} -> ${destination}`);
        }
        catch (error) {
            throw new Error(`Failed to copy file ${source} to ${destination}: ${error}`);
        }
    }
    /**
     * Write content to file
     */
    async writeFile(filePath, content) {
        try {
            await fs_extra_1.default.ensureDir(path_1.default.dirname(filePath));
            await fs_extra_1.default.writeFile(filePath, content, 'utf8');
            this.logger.debug(`Wrote file: ${filePath}`);
        }
        catch (error) {
            throw new Error(`Failed to write file ${filePath}: ${error}`);
        }
    }
    /**
     * Read file content
     */
    async readFile(filePath) {
        try {
            return await fs_extra_1.default.readFile(filePath, 'utf8');
        }
        catch (error) {
            throw new Error(`Failed to read file ${filePath}: ${error}`);
        }
    }
    /**
     * Check if file exists
     */
    async fileExists(filePath) {
        try {
            return await fs_extra_1.default.pathExists(filePath);
        }
        catch (error) {
            this.logger.debug(`Error checking file existence: ${error}`);
            return false;
        }
    }
    /**
     * Get relative path from current working directory
     */
    getRelativePath(filePath) {
        return path_1.default.relative(process.cwd(), filePath);
    }
    /**
     * Resolve path relative to project root
     */
    resolvePath(...pathSegments) {
        return path_1.default.resolve(...pathSegments);
    }
    /**
     * Get file extension
     */
    getExtension(filePath) {
        return path_1.default.extname(filePath);
    }
    /**
     * Get filename without extension
     */
    getBasename(filePath) {
        return path_1.default.basename(filePath, path_1.default.extname(filePath));
    }
}
exports.FileUtils = FileUtils;
//# sourceMappingURL=file-utils.js.map