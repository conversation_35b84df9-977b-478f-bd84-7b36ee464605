{"version": 3, "file": "create-project.js", "sourceRoot": "", "sources": ["../../src/commands/create-project.ts"], "names": [], "mappings": ";;;;;;AACA,wDAAgC;AAChC,uDAAgD;AAEhD,6EAAsE;AAOtE;;GAEG;AACH,MAAa,oBAAqB,SAAQ,6BAAW;IAGnD,YAAY,OAAO,GAAG,KAAK;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,uCAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACrE,CAAC;IAED,QAAQ,CAAC,OAAgB;QACvB,OAAO;aACJ,OAAO,CAAC,QAAQ,CAAC;aACjB,KAAK,CAAC,KAAK,CAAC;aACZ,WAAW,CAAC,mCAAmC,CAAC;aAChD,QAAQ,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;aACjD,MAAM,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;aACnE,MAAM,CAAC,aAAa,EAAE,0BAA0B,EAAE,KAAK,CAAC;aACxD,MAAM,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,IAAI,CAAC;aACrD,MAAM,CAAC,2BAA2B,EAAE,kDAAkD,CAAC;aACvF,MAAM,CAAC,eAAe,EAAE,gBAAgB,EAAE,KAAK,CAAC;aAChD,MAAM,CAAC,WAAW,EAAE,qDAAqD,EAAE,KAAK,CAAC;aACjF,MAAM,CAAC,KAAK,EAAE,WAAmB,EAAE,OAA6B,EAAE,EAAE;YACnE,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1D,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1D,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,WAAmB,EAAE,OAA6B;QAC9D,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,WAAW,EAAE,CAAC,CAAC;QAEnE,4BAA4B;QAC5B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAExE,mEAAmE;QACnE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9D,uCAAuC;QACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9D,uBAAuB;QACvB,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEjF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,WAAW,wBAAwB,CAAC,CAAC;QACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,WAAW,EAAE,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,WAAmB,EAAE,OAA6B;QAC/E,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO;gBACL,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,GAAG,WAAW,uBAAuB;gBAClD,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACpC;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,sBAAsB;gBAC/B,OAAO,EAAE,GAAG,WAAW,uBAAuB;aAC/C;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,SAAS;aACnB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,UAAU;gBACnB,OAAO,EAAE,OAAO;aACjB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,UAAU;gBACnB,OAAO,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC;gBAChE,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,OAA6B;QAClE,IAAI,OAAO,CAAC,QAAQ,KAAK,eAAe,EAAE,CAAC;YACzC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,CAAC;oBACN,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,YAAY;oBACtB,cAAc,EAAE,IAAI;oBACpB,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAoB,EAAE,CAAC;QACrC,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,OAAO,OAAO,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpD,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE7B,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;gBAC/C;oBACE,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,sBAAsB;oBAC/B,OAAO,EAAE,KAAK;iBACf;aACF,CAAC,CAAC;YAEH,OAAO,GAAG,cAAyB,CAAC;QACtC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACpC;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,eAAe;gBACxB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,0BAA0B;aACnF;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,eAAe;gBACxB,OAAO,EAAE;oBACP,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE;oBACrC,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE;oBACrC,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAAE;oBAC3C,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE;iBAC9C;gBACD,OAAO,EAAE,KAAK;aACf;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,kCAAkC;aAChG;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,WAAW;gBACpB,OAAO,EAAE;oBACP,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE;oBAC3C,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;oBACjC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;oBACrC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;oBACnC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;iBAChC;gBACD,OAAO,EAAE,YAAY;aACtB;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,yBAAyB;gBAClC,OAAO,EAAE,IAAI;aACd;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,IAAI;aACd;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,gCAAgC;gBACzC,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;QAEH,OAAO,OAAwB,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,OAA6B;QAClE,IAAI,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACpC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO;gBACL,IAAI,EAAE,UAAU;gBAChB,SAAS,EAAE,OAAO;gBAClB,OAAO,EAAE,UAAU;gBACnB,OAAO,EAAE,IAAI;gBACb,eAAe,EAAE,SAAS;gBAC1B,UAAU,EAAE,uBAAuB;aACpC,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YAChD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,+BAA+B;gBACxC,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACpC;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE,UAAU;aACpB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,qBAAqB;gBAC9B,OAAO,EAAE;oBACP,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;oBACjC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE;oBAChC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;oBACrC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;iBACpC;gBACD,OAAO,EAAE,OAAO;aACjB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE;oBACP,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,UAAU,EAAE;oBAC3C,EAAE,IAAI,EAAE,mBAAmB,EAAE,KAAK,EAAE,mBAAmB,EAAE;oBACzD,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE;oBAC7C,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE;oBACpC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;iBAChC;gBACD,OAAO,EAAE,UAAU;aACpB;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,kBAAkB;gBAC3B,OAAO,EAAE,IAAI;aACd;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,mBAAmB;gBAC5B,OAAO,EAAE;oBACP,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;oBACrC,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;oBACzC,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE;oBAC3C,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;iBAChC;gBACD,OAAO,EAAE,SAAS;aACnB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,eAAe;gBACxB,OAAO,EAAE,uBAAuB;aACjC;SACF,CAAC,CAAC;QAEH,OAAO,OAAyB,CAAC;IACnC,CAAC;CACF;AAlSD,oDAkSC"}