"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProjectCommand = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const base_command_js_1 = require("./base-command.js");
const project_generator_js_1 = require("../generators/project-generator.js");
/**
 * Command to create a new microservice project
 */
class CreateProjectCommand extends base_command_js_1.BaseCommand {
    constructor(verbose = false) {
        super(verbose);
        this.generator = new project_generator_js_1.ProjectGenerator(this.logger, this.fileUtils);
    }
    register(program) {
        program
            .command('create')
            .alias('new')
            .description('Create a new microservice project')
            .argument('<project-name>', 'Name of the project')
            .option('-o, --output-dir <dir>', 'Output directory', process.cwd())
            .option('-f, --force', 'Overwrite existing files', false)
            .option('-i, --interactive', 'Interactive mode', true)
            .option('-t, --template <template>', 'Project template (full, api-only, frontend-only)')
            .option('-v, --verbose', 'Verbose output', false)
            .option('--dry-run', 'Show what would be generated without creating files', false)
            .action(async (projectName, options) => {
            try {
                const command = new CreateProjectCommand(options.verbose);
                await command.execute(projectName, options);
            }
            catch (error) {
                const command = new CreateProjectCommand(options.verbose);
                command.handleError(error);
            }
        });
    }
    async execute(projectName, options) {
        this.validateOptions(options);
        this.logger.title(`Creating microservice project: ${projectName}`);
        // Get project configuration
        const projectConfig = await this.getProjectConfig(projectName, options);
        // Get service configurations based on template or interactive mode
        const services = await this.getServiceConfigurations(options);
        // Get frontend configuration if needed
        const frontend = await this.getFrontendConfiguration(options);
        // Generate the project
        await this.generator.generateProject(projectConfig, services, frontend, options);
        this.logger.success(`Project ${projectName} created successfully!`);
        this.logger.info(`Next steps:`);
        this.logger.info(`  cd ${projectName}`);
        this.logger.info(`  npm install`);
        this.logger.info(`  npm run dev`);
    }
    async getProjectConfig(projectName, options) {
        if (!options.interactive) {
            return {
                name: projectName,
                description: `${projectName} microservice project`,
                version: '1.0.0',
                license: 'MIT',
            };
        }
        const answers = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'description',
                message: 'Project description:',
                default: `${projectName} microservice project`,
            },
            {
                type: 'input',
                name: 'author',
                message: 'Author:',
            },
            {
                type: 'input',
                name: 'version',
                message: 'Version:',
                default: '1.0.0',
            },
            {
                type: 'list',
                name: 'license',
                message: 'License:',
                choices: ['MIT', 'Apache-2.0', 'GPL-3.0', 'BSD-3-Clause', 'ISC'],
                default: 'MIT',
            },
        ]);
        return {
            name: projectName,
            ...answers,
        };
    }
    async getServiceConfigurations(options) {
        if (options.template === 'frontend-only') {
            return [];
        }
        if (!options.interactive) {
            return [{
                    name: 'api',
                    type: 'api',
                    port: 3000,
                    database: 'postgresql',
                    authentication: true,
                    cors: true,
                    swagger: true,
                }];
        }
        const services = [];
        let addMore = true;
        while (addMore) {
            const serviceConfig = await this.getServiceConfig();
            services.push(serviceConfig);
            const { continueAdding } = await inquirer_1.default.prompt([
                {
                    type: 'confirm',
                    name: 'continueAdding',
                    message: 'Add another service?',
                    default: false,
                },
            ]);
            addMore = continueAdding;
        }
        return services;
    }
    async getServiceConfig() {
        const answers = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'name',
                message: 'Service name:',
                default: 'api',
                validate: (input) => input.trim().length > 0 || 'Service name is required',
            },
            {
                type: 'list',
                name: 'type',
                message: 'Service type:',
                choices: [
                    { name: 'API Service', value: 'api' },
                    { name: 'Web Service', value: 'web' },
                    { name: 'Worker Service', value: 'worker' },
                    { name: 'Gateway Service', value: 'gateway' },
                ],
                default: 'api',
            },
            {
                type: 'number',
                name: 'port',
                message: 'Port:',
                default: 3000,
                validate: (input) => (input > 0 && input < 65536) || 'Port must be between 1 and 65535',
            },
            {
                type: 'list',
                name: 'database',
                message: 'Database:',
                choices: [
                    { name: 'PostgreSQL', value: 'postgresql' },
                    { name: 'MySQL', value: 'mysql' },
                    { name: 'MongoDB', value: 'mongodb' },
                    { name: 'SQLite', value: 'sqlite' },
                    { name: 'None', value: 'none' },
                ],
                default: 'postgresql',
            },
            {
                type: 'confirm',
                name: 'authentication',
                message: 'Include authentication?',
                default: true,
            },
            {
                type: 'confirm',
                name: 'cors',
                message: 'Enable CORS?',
                default: true,
            },
            {
                type: 'confirm',
                name: 'swagger',
                message: 'Include Swagger documentation?',
                default: true,
            },
        ]);
        return answers;
    }
    async getFrontendConfiguration(options) {
        if (options.template === 'api-only') {
            return undefined;
        }
        if (!options.interactive) {
            return {
                name: 'frontend',
                framework: 'react',
                styling: 'tailwind',
                routing: true,
                stateManagement: 'zustand',
                apiBaseUrl: 'http://localhost:3000',
            };
        }
        const { includeFrontend } = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'includeFrontend',
                message: 'Include frontend application?',
                default: true,
            },
        ]);
        if (!includeFrontend) {
            return undefined;
        }
        const answers = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'name',
                message: 'Frontend app name:',
                default: 'frontend',
            },
            {
                type: 'list',
                name: 'framework',
                message: 'Frontend framework:',
                choices: [
                    { name: 'React', value: 'react' },
                    { name: 'Vue.js', value: 'vue' },
                    { name: 'Angular', value: 'angular' },
                    { name: 'Svelte', value: 'svelte' },
                ],
                default: 'react',
            },
            {
                type: 'list',
                name: 'styling',
                message: 'Styling framework:',
                choices: [
                    { name: 'Tailwind CSS', value: 'tailwind' },
                    { name: 'Styled Components', value: 'styled-components' },
                    { name: 'CSS Modules', value: 'css-modules' },
                    { name: 'Sass/SCSS', value: 'sass' },
                    { name: 'None', value: 'none' },
                ],
                default: 'tailwind',
            },
            {
                type: 'confirm',
                name: 'routing',
                message: 'Include routing?',
                default: true,
            },
            {
                type: 'list',
                name: 'stateManagement',
                message: 'State management:',
                choices: [
                    { name: 'Zustand', value: 'zustand' },
                    { name: 'Redux Toolkit', value: 'redux' },
                    { name: 'React Context', value: 'context' },
                    { name: 'None', value: 'none' },
                ],
                default: 'zustand',
            },
            {
                type: 'input',
                name: 'apiBaseUrl',
                message: 'API base URL:',
                default: 'http://localhost:3000',
            },
        ]);
        return answers;
    }
}
exports.CreateProjectCommand = CreateProjectCommand;
//# sourceMappingURL=create-project.js.map