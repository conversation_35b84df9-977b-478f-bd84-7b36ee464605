import { Command } from 'commander';
import { BaseCommand } from './base-command.js';
import { GeneratorOptions } from '../types/index.js';
interface CreateProjectOptions extends GeneratorOptions {
    interactive?: boolean;
    template?: string;
}
/**
 * Command to create a new microservice project
 */
export declare class CreateProjectCommand extends BaseCommand {
    private generator;
    constructor(verbose?: boolean);
    register(program: Command): void;
    execute(projectName: string, options: CreateProjectOptions): Promise<void>;
    private getProjectConfig;
    private getServiceConfigurations;
    private getServiceConfig;
    private getFrontendConfiguration;
}
export {};
//# sourceMappingURL=create-project.d.ts.map