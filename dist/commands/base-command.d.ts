import { Command } from 'commander';
import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
/**
 * Base class for all CLI commands
 */
export declare abstract class BaseCommand {
    protected logger: Logger;
    protected fileUtils: FileUtils;
    constructor(verbose?: boolean);
    /**
     * Abstract method to be implemented by each command
     */
    abstract execute(...args: unknown[]): Promise<void>;
    /**
     * Register the command with the CLI program
     */
    abstract register(program: Command): void;
    /**
     * Validate common options
     */
    protected validateOptions(options: {
        outputDir?: string;
        force?: boolean;
    }): void;
    /**
     * Handle common errors
     */
    protected handleError(error: unknown): void;
    /**
     * Confirm action with user
     */
    protected confirmAction(message: string): Promise<boolean>;
}
//# sourceMappingURL=base-command.d.ts.map