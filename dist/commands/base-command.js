"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCommand = void 0;
const logger_js_1 = require("../utils/logger.js");
const file_utils_js_1 = require("../utils/file-utils.js");
/**
 * Base class for all CLI commands
 */
class BaseCommand {
    constructor(verbose = false) {
        this.logger = new logger_js_1.Logger(verbose);
        this.fileUtils = new file_utils_js_1.FileUtils(this.logger);
    }
    /**
     * Validate common options
     */
    validateOptions(options) {
        if (options.outputDir && !options.outputDir.trim()) {
            throw new Error('Output directory cannot be empty');
        }
    }
    /**
     * Handle common errors
     */
    handleError(error) {
        if (error instanceof Error) {
            this.logger.error(error.message);
        }
        else {
            this.logger.error('An unexpected error occurred');
        }
        process.exit(1);
    }
    /**
     * Confirm action with user
     */
    async confirmAction(message) {
        const inquirer = await Promise.resolve().then(() => __importStar(require('inquirer')));
        const { confirm } = await inquirer.default.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message,
                default: false,
            },
        ]);
        return confirm;
    }
}
exports.BaseCommand = BaseCommand;
//# sourceMappingURL=base-command.js.map