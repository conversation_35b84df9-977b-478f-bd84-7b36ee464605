import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { AppController } from './app.controller.js';
import { AppService } from './app.service.js';
import { HealthModule } from './health/health.module.js';
import configuration from './config/configuration.js';
{{#service.database}}
import { DatabaseModule } from './database/database.module.js';
{{/service.database}}
{{#service.authentication}}
import { AuthModule } from './auth/auth.module.js';
{{/service.authentication}}
{{#service.microserviceTransport}}
import { MicroserviceModule } from './microservice/microservice.module.js';
{{/service.microserviceTransport}}

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    TerminusModule,
    HealthModule,
    {{#service.database}}
    DatabaseModule,
    {{/service.database}}
    {{#service.authentication}}
    AuthModule,
    {{/service.authentication}}
    {{#service.microserviceTransport}}
    MicroserviceModule,
    {{/service.microserviceTransport}}
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
