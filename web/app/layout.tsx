import type { Metada<PERSON> } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import './globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export const metadata: Metadata = {
  title: {
    default: 'Microservice CLI - Generate Modern Microservice Projects',
    template: '%s | Microservice CLI',
  },
  description: 'A powerful CLI tool for generating microservice projects with NestJS, Next.js, and modern technologies. Build scalable microservice architectures with ease.',
  keywords: [
    'microservices',
    'cli',
    'nestjs',
    'nextjs',
    'typescript',
    'docker',
    'kafka',
    'grpc',
    'code generator',
    'developer tools',
  ],
  authors: [{ name: 'Microservice CLI Team' }],
  creator: 'Microservice CLI',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://microservice-cli.dev',
    title: 'Microservice CLI - Generate Modern Microservice Projects',
    description: 'A powerful CLI tool for generating microservice projects with NestJS, Next.js, and modern technologies.',
    siteName: 'Microservice CLI',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Microservice CLI - Generate Modern Microservice Projects',
    description: 'A powerful CLI tool for generating microservice projects with NestJS, Next.js, and modern technologies.',
    creator: '@microservice_cli',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${jetbrainsMono.variable}`}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div className="relative flex min-h-screen flex-col">
          <div className="flex-1">{children}</div>
        </div>
        <Toaster
          position="bottom-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--card))',
              color: 'hsl(var(--card-foreground))',
              border: '1px solid hsl(var(--border))',
            },
          }}
        />
      </body>
    </html>
  );
}
