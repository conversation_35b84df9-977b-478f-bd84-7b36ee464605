import { Metadata } from 'next';
import { Navigation } from '@/components/navigation';
import { ProjectGenerator } from '@/components/generator/project-generator';
import { Footer } from '@/components/footer';

export const metadata: Metadata = {
  title: 'Project Generator - Microservice CLI',
  description: 'Generate your microservice project with our interactive web interface. Configure services, choose frameworks, and download your project.',
};

export default function GeneratorPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Navigation />
      
      <main className="flex-1 bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container py-8">
          <div className="mx-auto max-w-6xl">
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Project Generator
              </h1>
              <p className="mt-4 text-lg text-muted-foreground">
                Configure your microservice project and generate production-ready code
              </p>
            </div>
            
            <ProjectGenerator />
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
