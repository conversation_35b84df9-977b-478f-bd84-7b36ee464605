import { Metadata } from 'next';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ArrowRight, 
  Terminal, 
  Zap, 
  Shield, 
  Layers, 
  Code, 
  Database,
  Cloud,
  GitBranch,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navigation } from '@/components/navigation';
import { HeroSection } from '@/components/sections/hero-section';
import { FeaturesSection } from '@/components/sections/features-section';
import { TechStackSection } from '@/components/sections/tech-stack-section';
import { ExamplesSection } from '@/components/sections/examples-section';
import { Footer } from '@/components/footer';

export const metadata: Metadata = {
  title: 'Microservice CLI - Generate Modern Microservice Projects',
  description: 'A powerful CLI tool for generating microservice projects with NestJS, Next.js, and modern technologies. Build scalable microservice architectures with ease.',
};

export default function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Navigation />
      
      <main className="flex-1">
        <HeroSection />
        <FeaturesSection />
        <TechStackSection />
        <ExamplesSection />
        
        {/* CTA Section */}
        <section className="border-t bg-muted/50">
          <div className="container py-24 text-center">
            <div className="mx-auto max-w-3xl space-y-6">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Ready to Build Your Next Microservice?
              </h2>
              <p className="mx-auto max-w-[600px] text-muted-foreground md:text-xl">
                Join thousands of developers who are already using Microservice CLI to build 
                production-ready microservice architectures.
              </p>
              <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
                <Button size="lg" asChild>
                  <Link href="/generator">
                    <Terminal className="mr-2 h-5 w-5" />
                    Try the Generator
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/docs">
                    View Documentation
                  </Link>
                </Button>
              </div>
              
              {/* Stats */}
              <div className="mt-12 grid grid-cols-2 gap-8 sm:grid-cols-4">
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-primary">10K+</div>
                  <div className="text-sm text-muted-foreground">Projects Generated</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-primary">50+</div>
                  <div className="text-sm text-muted-foreground">Templates</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-primary">99%</div>
                  <div className="text-sm text-muted-foreground">Uptime</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-primary">24/7</div>
                  <div className="text-sm text-muted-foreground">Support</div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
