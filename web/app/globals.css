@import "tailwindcss";

@theme {
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(9% 0.026 285.75);
  --color-card: oklch(100% 0 0);
  --color-card-foreground: oklch(9% 0.026 285.75);
  --color-popover: oklch(100% 0 0);
  --color-popover-foreground: oklch(9% 0.026 285.75);
  --color-primary: oklch(47.78% 0.204 238.75);
  --color-primary-foreground: oklch(98% 0.013 285.75);
  --color-secondary: oklch(96.1% 0.013 285.75);
  --color-secondary-foreground: oklch(9% 0.026 285.75);
  --color-muted: oklch(96.1% 0.013 285.75);
  --color-muted-foreground: oklch(45.1% 0.015 285.75);
  --color-accent: oklch(96.1% 0.013 285.75);
  --color-accent-foreground: oklch(9% 0.026 285.75);
  --color-destructive: oklch(62.8% 0.257 29.23);
  --color-destructive-foreground: oklch(98% 0.013 285.75);
  --color-border: oklch(89.8% 0.013 285.75);
  --color-input: oklch(89.8% 0.013 285.75);
  --color-ring: oklch(47.78% 0.204 238.75);
  --radius: 0.5rem;

  --color-success: oklch(72.2% 0.131 142.5);
  --color-warning: oklch(84.3% 0.199 83.87);
  --color-info: oklch(69.71% 0.131 230.32);

  @media (prefers-color-scheme: dark) {
    --color-background: oklch(9% 0.026 285.75);
    --color-foreground: oklch(98% 0.013 285.75);
    --color-card: oklch(9% 0.026 285.75);
    --color-card-foreground: oklch(98% 0.013 285.75);
    --color-popover: oklch(9% 0.026 285.75);
    --color-popover-foreground: oklch(98% 0.013 285.75);
    --color-primary: oklch(69.71% 0.131 230.32);
    --color-primary-foreground: oklch(9% 0.026 285.75);
    --color-secondary: oklch(17.9% 0.013 285.75);
    --color-secondary-foreground: oklch(98% 0.013 285.75);
    --color-muted: oklch(17.9% 0.013 285.75);
    --color-muted-foreground: oklch(63.9% 0.013 285.75);
    --color-accent: oklch(17.9% 0.013 285.75);
    --color-accent-foreground: oklch(98% 0.013 285.75);
    --color-destructive: oklch(62.8% 0.257 29.23);
    --color-destructive-foreground: oklch(98% 0.013 285.75);
    --color-border: oklch(17.9% 0.013 285.75);
    --color-input: oklch(17.9% 0.013 285.75);
    --color-ring: oklch(69.71% 0.131 230.32);
  }
}

* {
  border-color: var(--color-border);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
}
.gradient-text {
  background: linear-gradient(to right, #2563eb, #9333ea, #4f46e5);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.gradient-bg {
  background: linear-gradient(to bottom right, #eff6ff, #eef2ff, #faf5ff);
}

@media (prefers-color-scheme: dark) {
  .gradient-bg {
    background: linear-gradient(to bottom right, #111827, #1e3a8a, #312e81);
  }
}

.glass {
  backdrop-filter: blur(8px);
  background-color: rgb(255 255 255 / 0.8);
  border: 1px solid rgb(255 255 255 / 0.2);
}

@media (prefers-color-scheme: dark) {
  .glass {
    backdrop-filter: blur(8px);
    background-color: rgb(17 24 39 / 0.8);
    border: 1px solid rgb(55 65 81 / 0.2);
  }
}

  .code-block {
    @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto;
  }

  .feature-card {
    @apply group relative overflow-hidden rounded-xl border bg-card p-6 transition-all duration-300 hover:shadow-lg hover:shadow-primary/10;
  }

  .feature-card::before {
    @apply absolute inset-0 bg-gradient-to-r from-primary/5 to-purple-500/5 opacity-0 transition-opacity duration-300;
    content: '';
  }

  .feature-card:hover::before {
    @apply opacity-100;
  }

  .btn-primary {
    @apply inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .animate-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Syntax highlighting overrides */
.hljs {
  @apply !bg-gray-900 !text-gray-100;
}

.hljs-keyword {
  @apply !text-purple-400;
}

.hljs-string {
  @apply !text-green-400;
}

.hljs-number {
  @apply !text-blue-400;
}

.hljs-comment {
  @apply !text-gray-500;
}
