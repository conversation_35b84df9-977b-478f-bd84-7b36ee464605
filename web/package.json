{"name": "microservice-cli-web", "version": "1.0.0", "description": "Web interface for the Microservice CLI tool", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write \"**/*.{js,ts,jsx,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,ts,jsx,tsx,json,md}\""}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-hook-form": "^7.47.0", "zod": "^3.22.0", "@hookform/resolvers": "^3.3.0", "react-syntax-highlighter": "^15.5.0", "jszip": "^3.10.0", "file-saver": "^2.0.5", "react-hot-toast": "^2.4.0", "zustand": "^4.4.0", "mustache": "^4.2.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-syntax-highlighter": "^15.5.0", "@types/file-saver": "^2.0.0", "@types/mustache": "^4.2.0", "typescript": "^5.2.0", "tailwindcss": "^4.0.0-alpha.25", "@tailwindcss/vite": "^4.0.0-alpha.25", "eslint": "^8.50.0", "eslint-config-next": "^14.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "engines": {"node": ">=18.0.0"}}