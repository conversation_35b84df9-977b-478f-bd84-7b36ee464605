import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { 
  ProjectConfig, 
  ServiceConfig, 
  FrontendConfig, 
  GeneratedProject,
  GeneratorOptions 
} from '@/types/project';

interface ProjectStore {
  // Project configuration
  project: ProjectConfig;
  services: ServiceConfig[];
  frontend?: FrontendConfig;
  options: GeneratorOptions;
  
  // Generated project
  generatedProject?: GeneratedProject;
  isGenerating: boolean;
  
  // UI state
  currentStep: number;
  completedSteps: number[];
  
  // Actions
  updateProject: (project: Partial<ProjectConfig>) => void;
  addService: (service: ServiceConfig) => void;
  updateService: (index: number, service: Partial<ServiceConfig>) => void;
  removeService: (index: number) => void;
  updateFrontend: (frontend: Partial<FrontendConfig>) => void;
  removeFrontend: () => void;
  updateOptions: (options: Partial<GeneratorOptions>) => void;
  
  // Generation
  generateProject: () => Promise<void>;
  clearGenerated: () => void;
  
  // Navigation
  setCurrentStep: (step: number) => void;
  markStepCompleted: (step: number) => void;
  
  // Reset
  reset: () => void;
}

const initialProject: ProjectConfig = {
  name: '',
  description: '',
  author: '',
  version: '1.0.0',
  license: 'MIT',
};

const initialService: ServiceConfig = {
  name: 'api',
  type: 'api',
  framework: 'nestjs',
  port: 3000,
  database: 'postgresql',
  authentication: true,
  cors: true,
  swagger: true,
  microserviceTransport: ['kafka', 'grpc'],
  messagePatterns: true,
};

const initialFrontend: FrontendConfig = {
  name: 'frontend',
  framework: 'nextjs',
  styling: 'tailwind',
  routing: true,
  stateManagement: 'zustand',
  apiBaseUrl: 'http://localhost:3000',
};

const initialOptions: GeneratorOptions = {
  outputDir: './generated-project',
  force: false,
  verbose: false,
  dryRun: false,
};

export const useProjectStore = create<ProjectStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        project: initialProject,
        services: [initialService],
        frontend: initialFrontend,
        options: initialOptions,
        generatedProject: undefined,
        isGenerating: false,
        currentStep: 0,
        completedSteps: [],

        // Project actions
        updateProject: (projectUpdate) =>
          set((state) => ({
            project: { ...state.project, ...projectUpdate },
          })),

        // Service actions
        addService: (service) =>
          set((state) => ({
            services: [...state.services, service],
          })),

        updateService: (index, serviceUpdate) =>
          set((state) => ({
            services: state.services.map((service, i) =>
              i === index ? { ...service, ...serviceUpdate } : service
            ),
          })),

        removeService: (index) =>
          set((state) => ({
            services: state.services.filter((_, i) => i !== index),
          })),

        // Frontend actions
        updateFrontend: (frontendUpdate) =>
          set((state) => ({
            frontend: state.frontend
              ? { ...state.frontend, ...frontendUpdate }
              : { ...initialFrontend, ...frontendUpdate },
          })),

        removeFrontend: () =>
          set(() => ({
            frontend: undefined,
          })),

        // Options actions
        updateOptions: (optionsUpdate) =>
          set((state) => ({
            options: { ...state.options, ...optionsUpdate },
          })),

        // Generation actions
        generateProject: async () => {
          set({ isGenerating: true });
          
          try {
            const { project, services, frontend, options } = get();
            
            // Simulate API call to generate project
            const response = await fetch('/api/generate', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                project,
                services,
                frontend,
                options,
              }),
            });

            if (!response.ok) {
              throw new Error('Failed to generate project');
            }

            const generatedProject = await response.json();
            
            set({ 
              generatedProject,
              isGenerating: false,
            });
          } catch (error) {
            console.error('Generation failed:', error);
            set({ isGenerating: false });
            throw error;
          }
        },

        clearGenerated: () =>
          set({ generatedProject: undefined }),

        // Navigation actions
        setCurrentStep: (step) =>
          set({ currentStep: step }),

        markStepCompleted: (step) =>
          set((state) => ({
            completedSteps: [...new Set([...state.completedSteps, step])],
          })),

        // Reset
        reset: () =>
          set({
            project: initialProject,
            services: [initialService],
            frontend: initialFrontend,
            options: initialOptions,
            generatedProject: undefined,
            isGenerating: false,
            currentStep: 0,
            completedSteps: [],
          }),
      }),
      {
        name: 'microservice-cli-store',
        partialize: (state) => ({
          project: state.project,
          services: state.services,
          frontend: state.frontend,
          options: state.options,
          currentStep: state.currentStep,
          completedSteps: state.completedSteps,
        }),
      }
    ),
    {
      name: 'microservice-cli-store',
    }
  )
);
