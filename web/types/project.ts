export interface ProjectConfig {
  name: string;
  description?: string;
  author?: string;
  version?: string;
  license?: string;
}

export interface ServiceConfig {
  name: string;
  type: ServiceType;
  framework: BackendFramework;
  port?: number;
  database?: DatabaseType;
  authentication?: boolean;
  cors?: boolean;
  swagger?: boolean;
  microserviceTransport?: MicroserviceTransport[];
  messagePatterns?: boolean;
}

export interface FrontendConfig {
  name: string;
  framework: FrontendFramework;
  styling: StylingFramework;
  routing?: boolean;
  stateManagement?: StateManagement;
  apiBaseUrl?: string;
}

export type ServiceType = 'api' | 'web' | 'worker' | 'gateway';

export type BackendFramework = 'express' | 'nestjs';

export type MicroserviceTransport = 'kafka' | 'grpc' | 'redis' | 'tcp' | 'nats' | 'rabbitmq';

export type DatabaseType = 'postgresql' | 'mysql' | 'mongodb' | 'sqlite' | 'none';

export type FrontendFramework = 'react' | 'vue' | 'angular' | 'svelte' | 'nextjs';

export type StylingFramework = 'tailwind' | 'styled-components' | 'css-modules' | 'sass' | 'none';

export type StateManagement = 'redux' | 'zustand' | 'context' | 'none';

export interface GeneratorOptions {
  outputDir?: string;
  force?: boolean;
  verbose?: boolean;
  dryRun?: boolean;
}

export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  tags: string[];
  config: {
    project: Partial<ProjectConfig>;
    services: ServiceConfig[];
    frontend?: FrontendConfig;
  };
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'file' | 'directory';
  size?: number;
}

export interface GeneratedProject {
  config: {
    project: ProjectConfig;
    services: ServiceConfig[];
    frontend?: FrontendConfig;
  };
  files: GeneratedFile[];
  structure: ProjectStructure;
  downloadUrl?: string;
}

export interface ProjectStructure {
  name: string;
  type: 'file' | 'directory';
  children?: ProjectStructure[];
  size?: number;
}

export const FRAMEWORK_OPTIONS = {
  backend: [
    { value: 'nestjs', label: 'NestJS', description: 'Enterprise-grade Node.js framework' },
    { value: 'express', label: 'Express.js', description: 'Fast, minimalist web framework' },
  ],
  frontend: [
    { value: 'nextjs', label: 'Next.js', description: 'React framework with SSR/SSG' },
    { value: 'react', label: 'React', description: 'Popular UI library with Vite' },
    { value: 'vue', label: 'Vue.js', description: 'Progressive JavaScript framework' },
    { value: 'angular', label: 'Angular', description: 'Platform for building web apps' },
    { value: 'svelte', label: 'Svelte', description: 'Cybernetically enhanced web apps' },
  ],
} as const;

export const DATABASE_OPTIONS = [
  { value: 'postgresql', label: 'PostgreSQL', description: 'Advanced open source relational database' },
  { value: 'mysql', label: 'MySQL', description: 'Popular relational database' },
  { value: 'mongodb', label: 'MongoDB', description: 'Document-oriented NoSQL database' },
  { value: 'sqlite', label: 'SQLite', description: 'Lightweight, file-based SQL database' },
  { value: 'none', label: 'None', description: 'No database integration' },
] as const;

export const TRANSPORT_OPTIONS = [
  { value: 'kafka', label: 'Kafka', description: 'Distributed event streaming platform' },
  { value: 'grpc', label: 'gRPC', description: 'High-performance RPC framework' },
  { value: 'redis', label: 'Redis', description: 'In-memory data structure store' },
  { value: 'tcp', label: 'TCP', description: 'Raw TCP transport' },
  { value: 'nats', label: 'NATS', description: 'Cloud native messaging system' },
  { value: 'rabbitmq', label: 'RabbitMQ', description: 'Message broker' },
] as const;

export const STYLING_OPTIONS = [
  { value: 'tailwind', label: 'Tailwind CSS', description: 'Utility-first CSS framework' },
  { value: 'styled-components', label: 'Styled Components', description: 'CSS-in-JS library' },
  { value: 'css-modules', label: 'CSS Modules', description: 'Localized CSS' },
  { value: 'sass', label: 'Sass/SCSS', description: 'CSS extension language' },
  { value: 'none', label: 'None', description: 'No styling framework' },
] as const;

export const STATE_MANAGEMENT_OPTIONS = [
  { value: 'zustand', label: 'Zustand', description: 'Small, fast state management' },
  { value: 'redux', label: 'Redux Toolkit', description: 'Predictable state container' },
  { value: 'context', label: 'React Context', description: 'Built-in React state management' },
  { value: 'none', label: 'None', description: 'No state management' },
] as const;
