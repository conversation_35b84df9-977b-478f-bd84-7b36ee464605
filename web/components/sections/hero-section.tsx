'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Terminal, Play, Github } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
      <div className="absolute left-0 right-0 top-0 -z-10 m-auto h-[310px] w-[310px] rounded-full bg-primary/20 opacity-20 blur-[100px]" />
      
      <div className="container relative py-24 lg:py-32">
        <div className="mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-6"
          >
            <Badge variant="secondary" className="mb-4">
              <Terminal className="mr-1 h-3 w-3" />
              v1.0.0 - Now with Tailwind v4 & NestJS
            </Badge>
            
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
              Generate Modern{' '}
              <span className="gradient-text">
                Microservices
              </span>{' '}
              in Seconds
            </h1>
            
            <p className="mx-auto max-w-[700px] text-lg text-muted-foreground sm:text-xl">
              A powerful CLI tool for generating production-ready microservice projects with 
              NestJS, Next.js, Kafka, gRPC, and modern technologies. Build scalable architectures 
              with zero configuration.
            </p>
            
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/generator">
                  <Play className="mr-2 h-5 w-5" />
                  Try Live Generator
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="https://github.com/your-org/microservice-cli" target="_blank">
                  <Github className="mr-2 h-4 w-4" />
                  View on GitHub
                </Link>
              </Button>
            </div>
            
            {/* Quick install */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mx-auto mt-12 max-w-md"
            >
              <div className="rounded-lg border bg-card p-4">
                <p className="mb-2 text-sm font-medium">Quick Install:</p>
                <div className="flex items-center justify-between rounded bg-muted p-2 font-mono text-sm">
                  <code>npm install -g microservice-cli</code>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      navigator.clipboard.writeText('npm install -g microservice-cli');
                    }}
                  >
                    Copy
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
        
        {/* Demo terminal */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          className="mx-auto mt-16 max-w-4xl"
        >
          <div className="rounded-lg border bg-card shadow-2xl">
            <div className="flex items-center gap-2 border-b px-4 py-3">
              <div className="h-3 w-3 rounded-full bg-red-500" />
              <div className="h-3 w-3 rounded-full bg-yellow-500" />
              <div className="h-3 w-3 rounded-full bg-green-500" />
              <span className="ml-2 text-sm text-muted-foreground">Terminal</span>
            </div>
            <div className="p-6 font-mono text-sm">
              <div className="space-y-2">
                <div className="flex items-center">
                  <span className="text-green-500">$</span>
                  <span className="ml-2">microservice-cli create my-app</span>
                </div>
                <div className="text-muted-foreground">
                  ✓ Project configuration
                </div>
                <div className="text-muted-foreground">
                  ✓ NestJS API service with Kafka & gRPC
                </div>
                <div className="text-muted-foreground">
                  ✓ Next.js frontend with Tailwind v4
                </div>
                <div className="text-muted-foreground">
                  ✓ Docker configuration
                </div>
                <div className="text-green-500">
                  🚀 Project my-app created successfully!
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
