'use client';

import * as React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useProjectStore } from '@/lib/store';
import { ProjectConfigStep } from './steps/project-config-step';
import { ServiceConfigStep } from './steps/service-config-step';
import { FrontendConfigStep } from './steps/frontend-config-step';
import { ReviewStep } from './steps/review-step';
import { GenerationStep } from './steps/generation-step';

const steps = [
  {
    id: 0,
    title: 'Project Configuration',
    description: 'Basic project information and settings',
    component: ProjectConfigStep,
  },
  {
    id: 1,
    title: 'Backend Services',
    description: 'Configure your microservices',
    component: ServiceConfigStep,
  },
  {
    id: 2,
    title: 'Frontend Application',
    description: 'Set up your frontend application',
    component: FrontendConfigStep,
  },
  {
    id: 3,
    title: 'Review & Generate',
    description: 'Review your configuration and generate the project',
    component: ReviewStep,
  },
  {
    id: 4,
    title: 'Download Project',
    description: 'Your project is ready for download',
    component: GenerationStep,
  },
];

export function ProjectGenerator() {
  const { 
    currentStep, 
    completedSteps, 
    setCurrentStep, 
    markStepCompleted,
    isGenerating 
  } = useProjectStore();

  const currentStepData = steps[currentStep];
  const CurrentStepComponent = currentStepData.component;

  const canGoNext = () => {
    // Add validation logic here
    return true;
  };

  const canGoPrevious = () => {
    return currentStep > 0;
  };

  const handleNext = () => {
    if (canGoNext() && currentStep < steps.length - 1) {
      markStepCompleted(currentStep);
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (canGoPrevious()) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepId: number) => {
    if (stepId <= Math.max(...completedSteps) + 1) {
      setCurrentStep(stepId);
    }
  };

  return (
    <div className="space-y-8">
      {/* Progress Steps */}
      <Card>
        <CardHeader>
          <CardTitle>Progress</CardTitle>
          <CardDescription>
            Follow these steps to configure and generate your microservice project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            {steps.slice(0, -1).map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center space-y-2">
                  <button
                    onClick={() => handleStepClick(step.id)}
                    className={`flex h-10 w-10 items-center justify-center rounded-full border-2 transition-colors ${
                      completedSteps.includes(step.id)
                        ? 'border-primary bg-primary text-primary-foreground'
                        : currentStep === step.id
                        ? 'border-primary bg-background text-primary'
                        : 'border-muted bg-background text-muted-foreground'
                    }`}
                    disabled={step.id > Math.max(...completedSteps, currentStep) + 1}
                  >
                    {completedSteps.includes(step.id) ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <span className="text-sm font-medium">{step.id + 1}</span>
                    )}
                  </button>
                  <div className="text-center">
                    <div className="text-sm font-medium">{step.title}</div>
                    <div className="text-xs text-muted-foreground hidden sm:block">
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < steps.length - 2 && (
                  <div className={`h-px flex-1 ${
                    completedSteps.includes(step.id) ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {currentStepData.title}
                    {completedSteps.includes(currentStep) && (
                      <Badge variant="success">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Completed
                      </Badge>
                    )}
                  </CardTitle>
                  <CardDescription>{currentStepData.description}</CardDescription>
                </div>
                <Badge variant="outline">
                  Step {currentStep + 1} of {steps.length}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <CurrentStepComponent />
            </CardContent>
          </Card>
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      {currentStep < steps.length - 1 && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={!canGoPrevious() || isGenerating}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canGoNext() || isGenerating}
          >
            Next
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
