'use client';

import * as React from 'react';
import { Plus, Trash2, Server, Database, Shield, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useProjectStore } from '@/lib/store';
import { 
  FRAMEWORK_OPTIONS, 
  DATABASE_OPTIONS, 
  TRANSPORT_OPTIONS,
  type ServiceConfig,
  type BackendFramework,
  type DatabaseType,
  type MicroserviceTransport
} from '@/types/project';

export function ServiceConfigStep() {
  const { services, addService, updateService, removeService } = useProjectStore();

  const addNewService = () => {
    const newService: ServiceConfig = {
      name: `service-${services.length + 1}`,
      type: 'api',
      framework: 'nestjs',
      port: 3000 + services.length,
      database: 'postgresql',
      authentication: true,
      cors: true,
      swagger: true,
      microserviceTransport: ['kafka'],
      messagePatterns: true,
    };
    addService(newService);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Backend Services</h3>
          <p className="text-sm text-muted-foreground">
            Configure your microservices with frameworks, databases, and communication methods
          </p>
        </div>
        <Button onClick={addNewService}>
          <Plus className="mr-2 h-4 w-4" />
          Add Service
        </Button>
      </div>

      <div className="space-y-4">
        {services.map((service, index) => (
          <Card key={index}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Server className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">{service.name}</CardTitle>
                  <Badge variant="outline">{service.framework}</Badge>
                </div>
                {services.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeService(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Basic</TabsTrigger>
                  <TabsTrigger value="database">Database</TabsTrigger>
                  <TabsTrigger value="microservices">Microservices</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                      label="Service Name"
                      value={service.name}
                      onChange={(e) => updateService(index, { name: e.target.value })}
                      placeholder="api-service"
                    />
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Framework</label>
                      <Select
                        value={service.framework}
                        onValueChange={(value: BackendFramework) => 
                          updateService(index, { framework: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {FRAMEWORK_OPTIONS.backend.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div>
                                <div className="font-medium">{option.label}</div>
                                <div className="text-xs text-muted-foreground">
                                  {option.description}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Input
                      label="Port"
                      type="number"
                      value={service.port}
                      onChange={(e) => updateService(index, { port: parseInt(e.target.value) })}
                      placeholder="3000"
                    />
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <Checkbox
                      checked={service.authentication}
                      onCheckedChange={(checked) => 
                        updateService(index, { authentication: !!checked })
                      }
                      label="Authentication"
                      description="JWT-based authentication"
                    />
                    
                    <Checkbox
                      checked={service.cors}
                      onCheckedChange={(checked) => 
                        updateService(index, { cors: !!checked })
                      }
                      label="CORS"
                      description="Cross-origin resource sharing"
                    />
                    
                    <Checkbox
                      checked={service.swagger}
                      onCheckedChange={(checked) => 
                        updateService(index, { swagger: !!checked })
                      }
                      label="Swagger"
                      description="API documentation"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="database" className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Database
                    </label>
                    <Select
                      value={service.database}
                      onValueChange={(value: DatabaseType) => 
                        updateService(index, { database: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {DATABASE_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">
                                {option.description}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>

                <TabsContent value="microservices" className="space-y-4">
                  {service.framework === 'nestjs' && (
                    <>
                      <div className="space-y-2">
                        <label className="text-sm font-medium flex items-center gap-2">
                          <Zap className="h-4 w-4" />
                          Transport Methods
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {TRANSPORT_OPTIONS.map((option) => (
                            <Checkbox
                              key={option.value}
                              checked={service.microserviceTransport?.includes(option.value as MicroserviceTransport)}
                              onCheckedChange={(checked) => {
                                const current = service.microserviceTransport || [];
                                const updated = checked
                                  ? [...current, option.value as MicroserviceTransport]
                                  : current.filter(t => t !== option.value);
                                updateService(index, { microserviceTransport: updated });
                              }}
                              label={option.label}
                              description={option.description}
                            />
                          ))}
                        </div>
                      </div>

                      <Checkbox
                        checked={service.messagePatterns}
                        onCheckedChange={(checked) => 
                          updateService(index, { messagePatterns: !!checked })
                        }
                        label="Message Patterns"
                        description="Include @MessagePattern and @EventPattern decorators"
                      />
                    </>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ))}
      </div>

      {services.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Server className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No services configured</h3>
            <p className="text-muted-foreground text-center mb-4">
              Add your first backend service to get started
            </p>
            <Button onClick={addNewService}>
              <Plus className="mr-2 h-4 w-4" />
              Add Service
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
