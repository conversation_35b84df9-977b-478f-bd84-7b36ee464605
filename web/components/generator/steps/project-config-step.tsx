'use client';

import * as React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useProjectStore } from '@/lib/store';

const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required').regex(/^[a-z0-9-]+$/, 'Use lowercase letters, numbers, and hyphens only'),
  description: z.string().optional(),
  author: z.string().optional(),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, 'Version must be in format x.y.z').default('1.0.0'),
  license: z.string().default('MIT'),
});

type ProjectFormData = z.infer<typeof projectSchema>;

const licenseOptions = [
  { value: 'MIT', label: 'MIT License' },
  { value: 'Apache-2.0', label: 'Apache License 2.0' },
  { value: 'GPL-3.0', label: 'GNU General Public License v3.0' },
  { value: 'BSD-3-Clause', label: 'BSD 3-Clause License' },
  { value: 'ISC', label: 'ISC License' },
];

export function ProjectConfigStep() {
  const { project, updateProject } = useProjectStore();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: project,
  });

  const watchedValues = watch();

  React.useEffect(() => {
    updateProject(watchedValues);
  }, [watchedValues, updateProject]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Configure the basic details for your microservice project
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Project Name"
              placeholder="my-microservice"
              error={errors.name?.message}
              helperText="Use lowercase letters, numbers, and hyphens"
              required
              {...register('name')}
            />
            
            <div className="space-y-2">
              <label className="text-sm font-medium">License</label>
              <Select
                value={watchedValues.license}
                onValueChange={(value) => setValue('license', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a license" />
                </SelectTrigger>
                <SelectContent>
                  {licenseOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Textarea
            label="Description"
            placeholder="A modern microservice project built with NestJS and Next.js"
            helperText="Brief description of your project"
            {...register('description')}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Author"
              placeholder="Your Name"
              {...register('author')}
            />
            
            <Input
              label="Version"
              placeholder="1.0.0"
              error={errors.version?.message}
              helperText="Semantic version (x.y.z)"
              {...register('version')}
            />
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Preview</CardTitle>
          <CardDescription>
            This is how your project will be configured
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg bg-muted p-4 font-mono text-sm">
            <div className="space-y-1">
              <div><span className="text-muted-foreground">Name:</span> {watchedValues.name || 'my-project'}</div>
              <div><span className="text-muted-foreground">Description:</span> {watchedValues.description || 'No description'}</div>
              <div><span className="text-muted-foreground">Author:</span> {watchedValues.author || 'Anonymous'}</div>
              <div><span className="text-muted-foreground">Version:</span> {watchedValues.version}</div>
              <div><span className="text-muted-foreground">License:</span> {watchedValues.license}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
