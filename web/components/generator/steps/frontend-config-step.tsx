'use client';

import * as React from 'react';
import { Monitor, Palette, Layers, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useProjectStore } from '@/lib/store';
import { 
  FRAMEWORK_OPTIONS, 
  STYLING_OPTIONS, 
  STATE_MANAGEMENT_OPTIONS,
  type FrontendFramework,
  type StylingFramework,
  type StateManagement
} from '@/types/project';

export function FrontendConfigStep() {
  const { frontend, updateFrontend, removeFrontend } = useProjectStore();

  const addFrontend = () => {
    updateFrontend({
      name: 'frontend',
      framework: 'nextjs',
      styling: 'tailwind',
      routing: true,
      stateManagement: 'zustand',
      apiBaseUrl: 'http://localhost:3000',
    });
  };

  if (!frontend) {
    return (
      <Card className="border-dashed">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Monitor className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No frontend configured</h3>
          <p className="text-muted-foreground text-center mb-4">
            Add a frontend application to create a full-stack project
          </p>
          <Button onClick={addFrontend}>
            <Monitor className="mr-2 h-4 w-4" />
            Add Frontend
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Monitor className="h-5 w-5 text-primary" />
              <CardTitle>Frontend Application</CardTitle>
              <Badge variant="outline">{frontend.framework}</Badge>
            </div>
            <Button variant="ghost" size="sm" onClick={removeFrontend}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            Configure your frontend application with modern frameworks and tools
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Configuration */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              Basic Configuration
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Application Name"
                value={frontend.name}
                onChange={(e) => updateFrontend({ name: e.target.value })}
                placeholder="frontend"
              />
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Framework</label>
                <Select
                  value={frontend.framework}
                  onValueChange={(value: FrontendFramework) => 
                    updateFrontend({ framework: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FRAMEWORK_OPTIONS.frontend.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Input
              label="API Base URL"
              value={frontend.apiBaseUrl}
              onChange={(e) => updateFrontend({ apiBaseUrl: e.target.value })}
              placeholder="http://localhost:3000"
              helperText="Base URL for your backend API"
            />
          </div>

          {/* Styling Configuration */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Styling & UI
            </h4>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Styling Framework</label>
              <Select
                value={frontend.styling}
                onValueChange={(value: StylingFramework) => 
                  updateFrontend({ styling: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {STYLING_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {option.description}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {frontend.styling === 'tailwind' && (
              <div className="p-3 bg-primary/5 border border-primary/20 rounded-lg">
                <div className="flex items-center gap-2 text-sm font-medium text-primary">
                  <Badge variant="secondary">New</Badge>
                  Tailwind CSS v4
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Your project will use the latest Tailwind CSS v4 with @theme configuration and OKLCH colors
                </p>
              </div>
            )}
          </div>

          {/* Advanced Configuration */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Advanced Features
            </h4>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">State Management</label>
              <Select
                value={frontend.stateManagement}
                onValueChange={(value: StateManagement) => 
                  updateFrontend({ stateManagement: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {STATE_MANAGEMENT_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {option.description}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Checkbox
              checked={frontend.routing}
              onCheckedChange={(checked) => 
                updateFrontend({ routing: !!checked })
              }
              label="Routing"
              description="Include client-side routing configuration"
            />
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Frontend Preview</CardTitle>
          <CardDescription>
            Configuration summary for your frontend application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg bg-muted p-4 font-mono text-sm space-y-2">
            <div><span className="text-muted-foreground">Framework:</span> {frontend.framework}</div>
            <div><span className="text-muted-foreground">Styling:</span> {frontend.styling}</div>
            <div><span className="text-muted-foreground">State Management:</span> {frontend.stateManagement}</div>
            <div><span className="text-muted-foreground">Routing:</span> {frontend.routing ? 'Enabled' : 'Disabled'}</div>
            <div><span className="text-muted-foreground">API URL:</span> {frontend.apiBaseUrl}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
