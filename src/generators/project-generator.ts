import path from 'path';
import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
import { TemplateEngine } from './template-engine.js';
import { FrontendGenerator } from './frontend-generator.js';
import {
  ProjectConfig,
  ServiceConfig,
  FrontendConfig,
  GeneratorOptions,
} from '../types/index.js';
import { ServiceGenerator } from './service-generator.js';

/**
 * Main project generator that orchestrates the creation of microservice projects
 */
export class ProjectGenerator {
  private logger: Logger;
  private fileUtils: FileUtils;
  private templateEngine: TemplateEngine;
  private serviceGenerator: ServiceGenerator;
  private frontendGenerator: FrontendGenerator;

  constructor(logger: Logger, fileUtils: FileUtils) {
    this.logger = logger;
    this.fileUtils = fileUtils;
    this.templateEngine = new TemplateEngine(logger, fileUtils);
    this.serviceGenerator = new ServiceGenerator(logger, fileUtils, this.templateEngine);
    this.frontendGenerator = new FrontendGenerator(logger, fileUtils, this.templateEngine);
  }

  /**
   * Generate a complete microservice project
   */
  async generateProject(
    projectConfig: ProjectConfig,
    services: ServiceConfig[],
    frontend: FrontendConfig | undefined,
    options: GeneratorOptions
  ): Promise<void> {
    const projectPath = path.join(options.outputDir, projectConfig.name);

    // Validate project setup
    await this.validateProjectSetup(projectPath, options);

    // Create project structure
    await this.createProjectStructure(projectPath, projectConfig, options);

    // Generate services
    if (services.length > 0) {
      await this.generateServices(projectPath, services, projectConfig, options);
    }

    // Generate frontend
    if (frontend) {
      await this.generateFrontend(projectPath, frontend, projectConfig, options);
    }

    // Generate Docker and orchestration files
    await this.generateOrchestration(projectPath, projectConfig, services, frontend, options);

    // Generate root project files
    await this.generateRootFiles(projectPath, projectConfig, services, frontend, options);
  }

  /**
   * Validate project setup before generation
   */
  private async validateProjectSetup(projectPath: string, options: GeneratorOptions): Promise<void> {
    const isEmpty = await this.fileUtils.isDirectoryEmpty(projectPath);
    
    if (!isEmpty && !options.force) {
      const shouldContinue = await this.confirmOverwrite(projectPath);
      if (!shouldContinue) {
        throw new Error('Project generation cancelled');
      }
    }

    if (options.dryRun) {
      this.logger.info('Dry run mode - no files will be created');
    }
  }

  /**
   * Create basic project structure
   */
  private async createProjectStructure(
    projectPath: string,
    _projectConfig: ProjectConfig,
    options: GeneratorOptions
  ): Promise<void> {
    this.logger.subtitle('Creating project structure...');

    if (!options.dryRun) {
      await this.fileUtils.ensureDirectory(projectPath);
      await this.fileUtils.ensureDirectory(path.join(projectPath, 'services'));
      await this.fileUtils.ensureDirectory(path.join(projectPath, 'docs'));
      await this.fileUtils.ensureDirectory(path.join(projectPath, 'scripts'));
    }

    this.logger.success('Project structure created');
  }

  /**
   * Generate all services
   */
  private async generateServices(
    projectPath: string,
    services: ServiceConfig[],
    projectConfig: ProjectConfig,
    options: GeneratorOptions
  ): Promise<void> {
    this.logger.subtitle('Generating services...');

    for (const service of services) {
      const servicePath = path.join(projectPath, 'services', service.name);
      
      if (!options.dryRun) {
        await this.fileUtils.ensureDirectory(servicePath);
      }

      await this.serviceGenerator.generateService(servicePath, service, projectConfig, options);
    }

    this.logger.success(`Generated ${services.length} service(s)`);
  }

  /**
   * Generate frontend application
   */
  private async generateFrontend(
    projectPath: string,
    frontend: FrontendConfig,
    projectConfig: ProjectConfig,
    options: GeneratorOptions
  ): Promise<void> {
    this.logger.subtitle('Generating frontend...');

    const frontendPath = path.join(projectPath, frontend.name);
    
    if (!options.dryRun) {
      await this.fileUtils.ensureDirectory(frontendPath);
    }

    await this.frontendGenerator.generateFrontend(frontendPath, frontend, projectConfig, options);

    this.logger.success('Frontend generated');
  }

  /**
   * Generate Docker and orchestration files
   */
  private async generateOrchestration(
    projectPath: string,
    _projectConfig: ProjectConfig,
    services: ServiceConfig[],
    frontend: FrontendConfig | undefined,
    options: GeneratorOptions
  ): Promise<void> {
    this.logger.subtitle('Generating orchestration files...');

    // Generate docker-compose.yml
    const dockerComposeContent = this.generateDockerCompose(services, frontend);

    if (!options.dryRun) {
      await this.fileUtils.writeFile(
        path.join(projectPath, 'docker-compose.yml'),
        dockerComposeContent
      );
    }

    this.logger.success('Orchestration files generated');
  }

  /**
   * Generate root project files
   */
  private async generateRootFiles(
    projectPath: string,
    projectConfig: ProjectConfig,
    services: ServiceConfig[],
    frontend: FrontendConfig | undefined,
    options: GeneratorOptions
  ): Promise<void> {
    this.logger.subtitle('Generating root project files...');

    // Generate README.md
    const readmeContent = this.generateReadme(projectConfig, services, frontend);
    
    // Generate package.json for root project
    const packageJsonContent = this.generateRootPackageJson(projectConfig, services, frontend);

    if (!options.dryRun) {
      await this.fileUtils.writeFile(path.join(projectPath, 'README.md'), readmeContent);
      await this.fileUtils.writeFile(path.join(projectPath, 'package.json'), packageJsonContent);
    }

    this.logger.success('Root project files generated');
  }

  /**
   * Confirm overwrite of existing directory
   */
  private async confirmOverwrite(projectPath: string): Promise<boolean> {
    this.logger.warn(`Directory ${projectPath} is not empty`);
    
    const inquirer = await import('inquirer');
    const { confirm } = await inquirer.default.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Do you want to continue and overwrite existing files?',
        default: false,
      },
    ]);

    return confirm as boolean;
  }

  /**
   * Generate docker-compose.yml content
   */
  private generateDockerCompose(services: ServiceConfig[], frontend: FrontendConfig | undefined): string {
    const serviceDefinitions = services.map(service => `
  ${service.name}:
    build: ./services/${service.name}
    ports:
      - "${service.port}:${service.port}"
    environment:
      - NODE_ENV=development
      - PORT=${service.port}
    volumes:
      - ./services/${service.name}:/app
      - /app/node_modules`).join('\n');

    const frontendDefinition = frontend ? `
  ${frontend.name}:
    build: ./${frontend.name}
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./${frontend.name}:/app
      - /app/node_modules` : '';

    return `version: '3.8'

services:${serviceDefinitions}${frontendDefinition}

networks:
  default:
    driver: bridge
`;
  }

  /**
   * Generate README.md content
   */
  private generateReadme(
    projectConfig: ProjectConfig,
    services: ServiceConfig[],
    frontend: FrontendConfig | undefined
  ): string {
    const servicesList = services.map(s => `- **${s.name}** (${s.type}) - Port ${s.port}`).join('\n');
    const frontendInfo = frontend ? `\n- **${frontend.name}** (${frontend.framework}) - Frontend application` : '';

    return `# ${projectConfig.name}

${projectConfig.description || 'A microservice project'}

## Services

${servicesList}${frontendInfo}

## Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Start all services:
   \`\`\`bash
   docker-compose up
   \`\`\`

3. Or start services individually:
   \`\`\`bash
   npm run dev:services
   \`\`\`

## Development

- Each service is located in the \`services/\` directory
${frontend ? `- Frontend application is in the \`${frontend.name}/\` directory` : ''}
- Use \`docker-compose\` for local development
- API documentation is available at each service's \`/docs\` endpoint

## License

${projectConfig.license || 'MIT'}
`;
  }

  /**
   * Generate root package.json content
   */
  private generateRootPackageJson(
    projectConfig: ProjectConfig,
    services: ServiceConfig[],
    frontend: FrontendConfig | undefined
  ): string {
    const serviceScripts = services.reduce((acc, service) => {
      acc[`dev:${service.name}`] = `cd services/${service.name} && npm run dev`;
      return acc;
    }, {} as Record<string, string>);

    const frontendScript = frontend ? {
      [`dev:${frontend.name}`]: `cd ${frontend.name} && npm run dev`
    } : {};

    const packageJson = {
      name: projectConfig.name,
      version: projectConfig.version || '1.0.0',
      description: projectConfig.description || '',
      private: true,
      scripts: {
        'dev:services': services.map(s => `npm run dev:${s.name}`).join(' & '),
        ...serviceScripts,
        ...frontendScript,
        'docker:up': 'docker-compose up',
        'docker:down': 'docker-compose down',
        'docker:build': 'docker-compose build',
      },
      author: projectConfig.author || '',
      license: projectConfig.license || 'MIT',
    };

    return JSON.stringify(packageJson, null, 2);
  }
}
