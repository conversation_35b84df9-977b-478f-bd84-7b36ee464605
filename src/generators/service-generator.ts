import path from 'path';
import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
import { TemplateEngine } from './template-engine.js';
import {
  ServiceConfig,
  ProjectConfig,
  GeneratorOptions,
  TemplateContext,
  ServiceTemplate,
} from '../types/index.js';

/**
 * Generator for backend microservices
 */
export class ServiceGenerator {
  private logger: Logger;
  private fileUtils: FileUtils;
  private templateEngine: TemplateEngine;

  constructor(logger: Logger, fileUtils: FileUtils, templateEngine: TemplateEngine) {
    this.logger = logger;
    this.fileUtils = fileUtils;
    this.templateEngine = templateEngine;
  }

  /**
   * Generate a backend service
   */
  async generateService(
    servicePath: string,
    serviceConfig: ServiceConfig,
    projectConfig: ProjectConfig,
    options: GeneratorOptions
  ): Promise<void> {
    this.logger.startSpinner(`Generating ${serviceConfig.name} service...`);

    try {
      const template = this.getServiceTemplate(serviceConfig);
      const context = this.createServiceContext(serviceConfig, projectConfig);

      // Generate service files
      await this.generateServiceFiles(servicePath, template, context, options);

      // Generate package.json
      await this.generatePackageJson(servicePath, serviceConfig, projectConfig, template, options);

      // Generate Dockerfile
      await this.generateDockerfile(servicePath, serviceConfig, options);

      // Generate environment files
      await this.generateEnvironmentFiles(servicePath, serviceConfig, options);

      this.logger.succeedSpinner(`Service ${serviceConfig.name} generated successfully`);
    } catch (error) {
      this.logger.failSpinner(`Failed to generate service ${serviceConfig.name}`);
      throw error;
    }
  }

  /**
   * Get service template based on configuration
   */
  private getServiceTemplate(serviceConfig: ServiceConfig): ServiceTemplate {
    const baseTemplate: ServiceTemplate = {
      name: 'express-api',
      description: 'Express.js API service',
      files: [
        { source: 'service/src/index.ts', destination: 'src/index.ts', isTemplate: true },
        { source: 'service/src/app.ts', destination: 'src/app.ts', isTemplate: true },
        { source: 'service/src/routes/index.ts', destination: 'src/routes/index.ts', isTemplate: true },
        { source: 'service/src/routes/health.ts', destination: 'src/routes/health.ts', isTemplate: true },
        { source: 'service/src/middleware/index.ts', destination: 'src/middleware/index.ts', isTemplate: true },
        { source: 'service/src/middleware/error.ts', destination: 'src/middleware/error.ts', isTemplate: true },
        { source: 'service/src/config/index.ts', destination: 'src/config/index.ts', isTemplate: true },
        { source: 'service/src/types/index.ts', destination: 'src/types/index.ts', isTemplate: true },
        { source: 'service/tsconfig.json', destination: 'tsconfig.json', isTemplate: true },
        { source: 'service/.gitignore', destination: '.gitignore', isTemplate: false },
      ],
      dependencies: [
        'express',
        'cors',
        'helmet',
        'morgan',
        'compression',
        'dotenv',
      ],
      devDependencies: [
        '@types/node',
        '@types/express',
        '@types/cors',
        '@types/morgan',
        'typescript',
        'ts-node',
        'nodemon',
        'jest',
        '@types/jest',
        'ts-jest',
        'supertest',
        '@types/supertest',
      ],
      scripts: {
        'start': 'node dist/index.js',
        'dev': 'nodemon src/index.ts',
        'build': 'tsc',
        'test': 'jest',
        'test:watch': 'jest --watch',
        'lint': 'eslint src/**/*.ts',
        'lint:fix': 'eslint src/**/*.ts --fix',
      },
    };

    // Add database-specific dependencies and files
    if (serviceConfig.database && serviceConfig.database !== 'none') {
      this.addDatabaseSupport(baseTemplate, serviceConfig.database);
    }

    // Add authentication support
    if (serviceConfig.authentication) {
      this.addAuthenticationSupport(baseTemplate);
    }

    // Add Swagger documentation
    if (serviceConfig.swagger) {
      this.addSwaggerSupport(baseTemplate);
    }

    return baseTemplate;
  }

  /**
   * Add database support to template
   */
  private addDatabaseSupport(template: ServiceTemplate, database: string): void {
    template.files.push(
      { source: 'service/src/database/index.ts', destination: 'src/database/index.ts', isTemplate: true },
      { source: 'service/src/models/index.ts', destination: 'src/models/index.ts', isTemplate: true }
    );

    switch (database) {
      case 'postgresql':
        template.dependencies.push('pg', 'knex');
        template.devDependencies.push('@types/pg');
        break;
      case 'mysql':
        template.dependencies.push('mysql2', 'knex');
        break;
      case 'mongodb':
        template.dependencies.push('mongoose');
        template.devDependencies.push('@types/mongoose');
        break;
      case 'sqlite':
        template.dependencies.push('sqlite3', 'knex');
        break;
    }
  }

  /**
   * Add authentication support to template
   */
  private addAuthenticationSupport(template: ServiceTemplate): void {
    template.files.push(
      { source: 'service/src/middleware/auth.ts', destination: 'src/middleware/auth.ts', isTemplate: true },
      { source: 'service/src/routes/auth.ts', destination: 'src/routes/auth.ts', isTemplate: true }
    );

    template.dependencies.push('jsonwebtoken', 'bcryptjs');
    template.devDependencies.push('@types/jsonwebtoken', '@types/bcryptjs');
  }

  /**
   * Add Swagger documentation support
   */
  private addSwaggerSupport(template: ServiceTemplate): void {
    template.files.push(
      { source: 'service/src/docs/swagger.ts', destination: 'src/docs/swagger.ts', isTemplate: true }
    );

    template.dependencies.push('swagger-ui-express', 'swagger-jsdoc');
    template.devDependencies.push('@types/swagger-ui-express', '@types/swagger-jsdoc');
  }

  /**
   * Create service context for templates
   */
  private createServiceContext(serviceConfig: ServiceConfig, projectConfig: ProjectConfig): TemplateContext {
    return this.templateEngine.createContext({
      project: projectConfig,
      service: serviceConfig,
    });
  }

  /**
   * Generate service files from templates
   */
  private async generateServiceFiles(
    servicePath: string,
    template: ServiceTemplate,
    context: TemplateContext,
    options: GeneratorOptions
  ): Promise<void> {
    if (options.dryRun) {
      this.logger.info(`Would generate ${template.files.length} files for service`);
      return;
    }

    await this.templateEngine.processTemplates(template.files, context, servicePath);
  }

  /**
   * Generate package.json for the service
   */
  private async generatePackageJson(
    servicePath: string,
    serviceConfig: ServiceConfig,
    projectConfig: ProjectConfig,
    template: ServiceTemplate,
    options: GeneratorOptions
  ): Promise<void> {
    const packageJson = {
      name: `${projectConfig.name}-${serviceConfig.name}`,
      version: projectConfig.version || '1.0.0',
      description: `${serviceConfig.name} service for ${projectConfig.name}`,
      main: 'dist/index.js',
      scripts: template.scripts,
      dependencies: this.createDependencyObject(template.dependencies),
      devDependencies: this.createDependencyObject(template.devDependencies),
      author: projectConfig.author || '',
      license: projectConfig.license || 'MIT',
      engines: {
        node: '>=16.0.0',
      },
    };

    if (!options.dryRun) {
      await this.fileUtils.writeFile(
        path.join(servicePath, 'package.json'),
        JSON.stringify(packageJson, null, 2)
      );
    }
  }

  /**
   * Generate Dockerfile for the service
   */
  private async generateDockerfile(
    servicePath: string,
    serviceConfig: ServiceConfig,
    options: GeneratorOptions
  ): Promise<void> {
    const dockerfileContent = `FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE ${serviceConfig.port}

USER node

CMD ["npm", "start"]
`;

    if (!options.dryRun) {
      await this.fileUtils.writeFile(path.join(servicePath, 'Dockerfile'), dockerfileContent);
    }
  }

  /**
   * Generate environment files
   */
  private async generateEnvironmentFiles(
    servicePath: string,
    serviceConfig: ServiceConfig,
    options: GeneratorOptions
  ): Promise<void> {
    const envContent = `NODE_ENV=development
PORT=${serviceConfig.port}
${serviceConfig.database !== 'none' ? `DATABASE_URL=\nDATABASE_NAME=${serviceConfig.name}_db` : ''}
${serviceConfig.authentication ? 'JWT_SECRET=your-secret-key' : ''}
`;

    const envExampleContent = envContent.replace(/=.*/g, '=');

    if (!options.dryRun) {
      await this.fileUtils.writeFile(path.join(servicePath, '.env'), envContent);
      await this.fileUtils.writeFile(path.join(servicePath, '.env.example'), envExampleContent);
    }
  }

  /**
   * Create dependency object with latest versions
   */
  private createDependencyObject(dependencies: string[]): Record<string, string> {
    return dependencies.reduce((acc, dep) => {
      acc[dep] = 'latest';
      return acc;
    }, {} as Record<string, string>);
  }
}
