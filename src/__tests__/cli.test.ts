import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs-extra';

const execAsync = promisify(exec);

describe('Microservice CLI', () => {
  const testOutputDir = path.join(__dirname, '../../test-output');
  const cliPath = path.join(__dirname, '../../dist/index.js');

  beforeEach(async () => {
    // Clean up test output directory
    await fs.remove(testOutputDir);
    await fs.ensureDir(testOutputDir);
  });

  afterEach(async () => {
    // Clean up test output directory
    await fs.remove(testOutputDir);
  });

  it('should display help information', async () => {
    const { stdout } = await execAsync(`node ${cliPath} --help`);
    
    expect(stdout).toContain('microservice-cli');
    expect(stdout).toContain('create');
    expect(stdout).toContain('A CLI tool for generating microservice projects');
  });

  it('should display version information', async () => {
    const { stdout } = await execAsync(`node ${cliPath} --version`);
    
    expect(stdout).toMatch(/\d+\.\d+\.\d+/);
  });

  it('should run dry-run mode successfully', async () => {
    const projectName = 'test-project';
    const command = `node ${cliPath} create ${projectName} --dry-run --output-dir ${testOutputDir}`;
    
    // This test would need to be run with pre-configured answers
    // For now, we'll just test that the command doesn't crash
    try {
      await execAsync(command, { timeout: 5000 });
      // The command will timeout waiting for input, but that's expected
    } catch (error: any) {
      // Expect timeout error since we're not providing interactive input
      expect(error.killed).toBe(true);
    }
  });

  it('should validate project name', async () => {
    try {
      await execAsync(`node ${cliPath} create "" --dry-run`);
      fail('Should have thrown an error for empty project name');
    } catch (error: any) {
      expect(error.code).toBe(1);
    }
  });
});

describe('Template Engine', () => {
  it('should process mustache templates correctly', () => {
    const Mustache = require('mustache');
    
    const template = 'Hello {{name}}, welcome to {{service.name}}!';
    const context = {
      name: 'World',
      service: {
        name: 'test-service'
      }
    };
    
    const result = Mustache.render(template, context);
    expect(result).toBe('Hello World, welcome to test-service!');
  });
});

describe('File Utils', () => {
  const testDir = path.join(__dirname, '../../test-files');

  beforeEach(async () => {
    await fs.ensureDir(testDir);
  });

  afterEach(async () => {
    await fs.remove(testDir);
  });

  it('should check if directory is empty', async () => {
    const { FileUtils } = await import('../utils/file-utils.js');
    const { Logger } = await import('../utils/logger.js');
    
    const logger = new Logger(false);
    const fileUtils = new FileUtils(logger);
    
    // Empty directory
    expect(await fileUtils.isDirectoryEmpty(testDir)).toBe(true);
    
    // Add a file
    await fs.writeFile(path.join(testDir, 'test.txt'), 'test content');
    expect(await fileUtils.isDirectoryEmpty(testDir)).toBe(false);
  });

  it('should write and read files correctly', async () => {
    const { FileUtils } = await import('../utils/file-utils.js');
    const { Logger } = await import('../utils/logger.js');
    
    const logger = new Logger(false);
    const fileUtils = new FileUtils(logger);
    
    const testFile = path.join(testDir, 'test.txt');
    const content = 'Hello, World!';
    
    await fileUtils.writeFile(testFile, content);
    const readContent = await fileUtils.readFile(testFile);
    
    expect(readContent).toBe(content);
  });
});

describe('Logger', () => {
  it('should create logger instance', async () => {
    const { Logger } = await import('../utils/logger.js');
    
    const logger = new Logger(false);
    expect(logger).toBeDefined();
    
    // Test that methods don't throw
    expect(() => {
      logger.info('Test info message');
      logger.success('Test success message');
      logger.warn('Test warning message');
      logger.error('Test error message');
    }).not.toThrow();
  });

  it('should handle verbose mode', async () => {
    const { Logger } = await import('../utils/logger.js');
    
    const verboseLogger = new Logger(true);
    expect(verboseLogger).toBeDefined();
    
    // Test debug message in verbose mode
    expect(() => {
      verboseLogger.debug('Test debug message');
    }).not.toThrow();
  });
});
