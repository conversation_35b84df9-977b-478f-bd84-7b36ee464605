/**
 * Core types for the microservice CLI tool
 */

export interface ProjectConfig {
  name: string;
  description?: string;
  author?: string;
  version?: string;
  license?: string;
}

export interface ServiceConfig {
  name: string;
  type: ServiceType;
  framework: BackendFramework;
  port?: number;
  database?: DatabaseType;
  authentication?: boolean;
  cors?: boolean;
  swagger?: boolean;
  microserviceTransport?: MicroserviceTransport[];
  messagePatterns?: boolean;
}

export interface FrontendConfig {
  name: string;
  framework: FrontendFramework;
  styling: StylingFramework;
  routing?: boolean;
  stateManagement?: StateManagement;
  apiBaseUrl?: string;
}

export type ServiceType = 'api' | 'web' | 'worker' | 'gateway';

export type BackendFramework = 'express' | 'nestjs';

export type MicroserviceTransport = 'kafka' | 'grpc' | 'redis' | 'tcp' | 'nats' | 'rabbitmq';

export type DatabaseType = 'postgresql' | 'mysql' | 'mongodb' | 'sqlite' | 'none';

export type FrontendFramework = 'react' | 'vue' | 'angular' | 'svelte' | 'nextjs';

export type StylingFramework = 'tailwind' | 'styled-components' | 'css-modules' | 'sass' | 'none';

export type StateManagement = 'redux' | 'zustand' | 'context' | 'none';

export interface TemplateContext {
  project: ProjectConfig;
  service?: ServiceConfig;
  frontend?: FrontendConfig;
  timestamp: string;
  year: number;
}

export interface GeneratorOptions {
  outputDir: string;
  force?: boolean;
  verbose?: boolean;
  dryRun?: boolean;
}

export interface TemplateFile {
  source: string;
  destination: string;
  isTemplate: boolean;
}

export interface ServiceTemplate {
  name: string;
  description: string;
  files: TemplateFile[];
  dependencies: string[];
  devDependencies: string[];
  scripts: Record<string, string>;
}
