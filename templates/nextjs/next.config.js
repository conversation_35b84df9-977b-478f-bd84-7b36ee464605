/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '{{frontend.apiBaseUrl}}/api/:path*',
      },
    ];
  },
  env: {
    API_BASE_URL: process.env.API_BASE_URL || '{{frontend.apiBaseUrl}}',
  },
  // Enable Tailwind v4 CSS imports
  transpilePackages: ['tailwindcss'],
};

module.exports = nextConfig;
