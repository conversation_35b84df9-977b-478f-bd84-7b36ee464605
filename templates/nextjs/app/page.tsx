import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Home - {{project.name}}',
  description: 'Welcome to {{project.name}}',
};

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to {{project.name}}
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            {{project.description}}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                API Documentation
              </h3>
              <p className="text-gray-600 mb-4">
                Explore the API endpoints and documentation
              </p>
              <Link 
                href="{{frontend.apiBaseUrl}}/docs"
                className="text-blue-600 hover:text-blue-800 font-medium"
                target="_blank"
                rel="noopener noreferrer"
              >
                View Docs →
              </Link>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Health Check
              </h3>
              <p className="text-gray-600 mb-4">
                Check the status of backend services
              </p>
              <Link 
                href="/api/health"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Check Status →
              </Link>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Get Started
              </h3>
              <p className="text-gray-600 mb-4">
                Learn how to use this application
              </p>
              <Link 
                href="/getting-started"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Learn More →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
