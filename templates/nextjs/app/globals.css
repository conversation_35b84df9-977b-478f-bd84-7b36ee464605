@import "tailwindcss";

@theme {
  --color-primary: oklch(47.78% 0.204 238.75);
  --color-primary-foreground: oklch(98% 0.013 285.75);
  --color-secondary: oklch(96.1% 0.013 285.75);
  --color-secondary-foreground: oklch(9% 0.026 285.75);
  --color-accent: oklch(96.1% 0.013 285.75);
  --color-accent-foreground: oklch(9% 0.026 285.75);
  --color-destructive: oklch(62.8% 0.257 29.23);
  --color-destructive-foreground: oklch(98% 0.013 285.75);
  --color-muted: oklch(96.1% 0.013 285.75);
  --color-muted-foreground: oklch(45.1% 0.015 285.75);
  --color-border: oklch(89.8% 0.013 285.75);
  --color-input: oklch(89.8% 0.013 285.75);
  --color-ring: oklch(47.78% 0.204 238.75);
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(9% 0.026 285.75);
  --color-card: oklch(100% 0 0);
  --color-card-foreground: oklch(9% 0.026 285.75);
  --radius: 0.5rem;

  @media (prefers-color-scheme: dark) {
    --color-primary: oklch(69.71% 0.131 230.32);
    --color-primary-foreground: oklch(9% 0.026 285.75);
    --color-secondary: oklch(17.9% 0.013 285.75);
    --color-secondary-foreground: oklch(98% 0.013 285.75);
    --color-accent: oklch(17.9% 0.013 285.75);
    --color-accent-foreground: oklch(98% 0.013 285.75);
    --color-destructive: oklch(62.8% 0.257 29.23);
    --color-destructive-foreground: oklch(98% 0.013 285.75);
    --color-muted: oklch(17.9% 0.013 285.75);
    --color-muted-foreground: oklch(63.9% 0.013 285.75);
    --color-border: oklch(17.9% 0.013 285.75);
    --color-input: oklch(17.9% 0.013 285.75);
    --color-ring: oklch(69.71% 0.131 230.32);
    --color-background: oklch(9% 0.026 285.75);
    --color-foreground: oklch(98% 0.013 285.75);
    --color-card: oklch(9% 0.026 285.75);
    --color-card-foreground: oklch(98% 0.013 285.75);
  }
}

html {
  color-scheme: light dark;
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.gradient-text {
  background: linear-gradient(to right, #2563eb, #9333ea, #4f46e5);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.gradient-bg {
  background: linear-gradient(to bottom right, #eff6ff, #eef2ff, #faf5ff);
}

@media (prefers-color-scheme: dark) {
  .gradient-bg {
    background: linear-gradient(to bottom right, #111827, #1e3a8a, #312e81);
  }
}

.glass {
  backdrop-filter: blur(8px);
  background-color: rgb(255 255 255 / 0.8);
  border: 1px solid rgb(255 255 255 / 0.2);
}

@media (prefers-color-scheme: dark) {
  .glass {
    backdrop-filter: blur(8px);
    background-color: rgb(17 24 39 / 0.8);
    border: 1px solid rgb(55 65 81 / 0.2);
  }
}

.feature-card {
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
  background-color: var(--color-card);
  border-radius: var(--radius);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgb(59 130 246 / 0.05), rgb(147 51 234 / 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-card:hover {
  box-shadow: 0 10px 25px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  transform: translateY(-2px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--color-muted);
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-muted-foreground);
}
