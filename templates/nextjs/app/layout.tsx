import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
{{#frontend.stateManagement}}
{{#if (eq frontend.stateManagement "redux")}}
import { Providers } from '../lib/store/providers';
{{/if}}
{{/frontend.stateManagement}}

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: '{{project.name}} - {{frontend.name}}',
  description: '{{project.description}}',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {{#frontend.stateManagement}}
        {{#if (eq frontend.stateManagement "redux")}}
        <Providers>
          {children}
        </Providers>
        {{else}}
        {children}
        {{/if}}
        {{else}}
        {children}
        {{/frontend.stateManagement}}
      </body>
    </html>
  );
}
