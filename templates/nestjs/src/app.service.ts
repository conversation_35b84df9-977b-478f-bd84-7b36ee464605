import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      service: '{{service.name}}',
      description: '{{service.name}} microservice',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/api/health',
        {{#service.authentication}}
        auth: '/api/auth',
        {{/service.authentication}}
        {{#service.swagger}}
        docs: '/docs',
        {{/service.swagger}}
      },
    };
  }

  getVersion() {
    return {
      version: '1.0.0',
      service: '{{service.name}}',
      timestamp: new Date().toISOString(),
    };
  }
}
