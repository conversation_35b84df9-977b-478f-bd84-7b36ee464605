import { Controller } from '@nestjs/common';
import { MessagePattern, EventPattern, Payload } from '@nestjs/microservices';
import { MicroserviceService } from './microservice.service.js';

@Controller()
export class MicroserviceController {
  constructor(private readonly microserviceService: MicroserviceService) {}

  {{#service.messagePatterns}}
  @MessagePattern('{{service.name}}.get.info')
  async getServiceInfo(@Payload() data: any) {
    return this.microserviceService.getServiceInfo(data);
  }

  @MessagePattern('{{service.name}}.process.data')
  async processData(@Payload() data: any) {
    return this.microserviceService.processData(data);
  }

  @EventPattern('{{service.name}}.event.created')
  async handleCreatedEvent(@Payload() data: any) {
    return this.microserviceService.handleCreatedEvent(data);
  }

  @EventPattern('{{service.name}}.event.updated')
  async handleUpdatedEvent(@Payload() data: any) {
    return this.microserviceService.handleUpdatedEvent(data);
  }

  @EventPattern('{{service.name}}.event.deleted')
  async handleDeletedEvent(@Payload() data: any) {
    return this.microserviceService.handleDeletedEvent(data);
  }
  {{/service.messagePatterns}}
}
