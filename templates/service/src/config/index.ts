import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface Config {
  port: number;
  nodeEnv: string;
  {{#service.database}}
  database: {
    url: string;
    name: string;
  };
  {{/service.database}}
  {{#service.authentication}}
  jwt: {
    secret: string;
    expiresIn: string;
  };
  {{/service.authentication}}
}

const config: Config = {
  port: parseInt(process.env.PORT || '{{service.port}}', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  {{#service.database}}
  database: {
    url: process.env.DATABASE_URL || 'postgresql://localhost:5432/{{service.name}}_db',
    name: process.env.DATABASE_NAME || '{{service.name}}_db',
  },
  {{/service.database}}
  {{#service.authentication}}
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  {{/service.authentication}}
};

export default config;
