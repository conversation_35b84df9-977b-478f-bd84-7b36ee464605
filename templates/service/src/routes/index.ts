import { Router } from 'express';
import healthRoutes from './health.js';
{{#service.authentication}}
import authRoutes from './auth.js';
{{/service.authentication}}

const router = Router();

// Health routes
router.use('/health', healthRoutes);

{{#service.authentication}}
// Authentication routes
router.use('/auth', authRoutes);
{{/service.authentication}}

// API routes
router.get('/', (req, res) => {
  res.json({
    message: 'Welcome to {{service.name}} API',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      {{#service.authentication}}
      auth: '/api/auth',
      {{/service.authentication}}
      {{#service.swagger}}
      docs: '/docs',
      {{/service.swagger}}
    },
  });
});

export default router;
