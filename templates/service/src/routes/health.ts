import { Router, Request, Response } from 'express';

const router = Router();

interface HealthResponse {
  status: 'healthy' | 'unhealthy';
  service: string;
  timestamp: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  {{#service.database}}
  database?: 'connected' | 'disconnected';
  {{/service.database}}
}

router.get('/', (req: Request, res: Response) => {
  const healthData: HealthResponse = {
    status: 'healthy',
    service: '{{service.name}}',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    {{#service.database}}
    database: 'connected', // TODO: Implement actual database health check
    {{/service.database}}
  };

  res.status(200).json(healthData);
});

router.get('/ready', (req: Request, res: Response) => {
  // Readiness probe - check if service is ready to handle requests
  res.status(200).json({
    status: 'ready',
    service: '{{service.name}}',
    timestamp: new Date().toISOString(),
  });
});

router.get('/live', (req: Request, res: Response) => {
  // Liveness probe - check if service is alive
  res.status(200).json({
    status: 'alive',
    service: '{{service.name}}',
    timestamp: new Date().toISOString(),
  });
});

export default router;
