import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import routes from './routes/index.js';
import { errorHandler } from './middleware/error.js';

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration
{{#service.cors}}
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true,
}));
{{/service.cors}}

// Logging
app.use(morgan('combined'));

// Compression
app.use(compression());

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api', routes);

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: '{{service.name}}',
    timestamp: new Date().toISOString(),
  });
});

// Error handling
app.use(errorHandler);

export default app;
