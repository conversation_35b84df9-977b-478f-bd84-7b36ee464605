import app from './app.js';
import config from './config/index.js';

const PORT = config.port || {{service.port}};

app.listen(PORT, () => {
  console.log(`🚀 {{service.name}} service running on port ${PORT}`);
  console.log(`📚 API documentation available at http://localhost:${PORT}/docs`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
