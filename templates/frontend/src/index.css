@import "tailwindcss";

@theme {
  --color-primary: oklch(47.78% 0.204 238.75);
  --color-primary-foreground: oklch(98% 0.013 285.75);
  --color-secondary: oklch(96.1% 0.013 285.75);
  --color-secondary-foreground: oklch(9% 0.026 285.75);
  --color-accent: oklch(96.1% 0.013 285.75);
  --color-accent-foreground: oklch(9% 0.026 285.75);
  --color-destructive: oklch(62.8% 0.257 29.23);
  --color-destructive-foreground: oklch(98% 0.013 285.75);
  --color-muted: oklch(96.1% 0.013 285.75);
  --color-muted-foreground: oklch(45.1% 0.015 285.75);
  --color-border: oklch(89.8% 0.013 285.75);
  --color-input: oklch(89.8% 0.013 285.75);
  --color-ring: oklch(47.78% 0.204 238.75);
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(9% 0.026 285.75);
  --color-card: oklch(100% 0 0);
  --color-card-foreground: oklch(9% 0.026 285.75);
  --radius: 0.5rem;

  @media (prefers-color-scheme: dark) {
    --color-primary: oklch(69.71% 0.131 230.32);
    --color-primary-foreground: oklch(9% 0.026 285.75);
    --color-secondary: oklch(17.9% 0.013 285.75);
    --color-secondary-foreground: oklch(98% 0.013 285.75);
    --color-accent: oklch(17.9% 0.013 285.75);
    --color-accent-foreground: oklch(98% 0.013 285.75);
    --color-destructive: oklch(62.8% 0.257 29.23);
    --color-destructive-foreground: oklch(98% 0.013 285.75);
    --color-muted: oklch(17.9% 0.013 285.75);
    --color-muted-foreground: oklch(63.9% 0.013 285.75);
    --color-border: oklch(17.9% 0.013 285.75);
    --color-input: oklch(17.9% 0.013 285.75);
    --color-ring: oklch(69.71% 0.131 230.32);
    --color-background: oklch(9% 0.026 285.75);
    --color-foreground: oklch(98% 0.013 285.75);
    --color-card: oklch(9% 0.026 285.75);
    --color-card-foreground: oklch(98% 0.013 285.75);
  }
}

body {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.gradient-text {
  background: linear-gradient(to right, #2563eb, #9333ea, #4f46e5);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.feature-card {
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
  background-color: var(--color-card);
  border-radius: var(--radius);
  padding: 1.5rem;
}

.feature-card:hover {
  box-shadow: 0 10px 25px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  transform: translateY(-2px);
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-secondary-foreground);
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--color-border);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
}
